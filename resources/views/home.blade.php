@extends('layouts.app')

@section('styles')
<style>
/* Enhanced Toast Styles */
.vote-update-toast {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vote-update-toast:hover {
    transform: translateX(-5px) !important;
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
}

/* Evidence Display Styles */
.evidence-container {
    min-width: 180px;
}

.latest-evidence-preview {
    max-height: 100px;
    overflow-y: auto;
}

.evidence-item {
    background: rgba(248, 250, 252, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.evidence-info {
    min-width: 0;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 0.7rem;
    line-height: 1.2;
}

.badge-sm {
    font-size: 0.6rem;
    padding: 1px 4px;
}

.evidence-actions .btn-xs {
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.evidence-item:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: rgba(203, 213, 225, 0.8);
}

/* Compact Dashboard Improvements */
.dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Compact Stats Cards */
.quick-stat {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Live indicator pulse */
.badge.bg-success {
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* Compact chart containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 15px 0;
}

/* Enhanced notification styles */
#vote-toast-container .vote-update-toast {
    font-family: 'Poppins', sans-serif;
}

/* Professional Compact Dashboard Card Styles */
.dashboard-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
    font-size: 0.9rem;
}

.hover-item:hover {
    background-color: rgba(0,0,0,0.04) !important;
    transform: translateX(1px);
}

/* Main Dashboard Three-Column Layout */
.main-dashboard-section {
    margin-bottom: 2rem;
}

.main-dashboard-section .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* Custom scrollbar for compact sections */
.card-body::-webkit-scrollbar {
    width: 3px;
}

.card-body::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.03);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.15);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.25);
}

/* Compact typography */
.dashboard-card .fw-bold {
    font-weight: 600 !important;
}

.dashboard-card .text-muted {
    color: #6c757d !important;
}

/* Evidence buttons styling */
.view-evidence-btn {
    transition: all 0.2s ease;
}

.view-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.download-evidence-btn {
    transition: all 0.2s ease;
}

.download-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    #vote-toast-container {
        right: 10px !important;
        max-width: calc(100vw - 20px) !important;
    }

    .vote-update-toast {
        padding: 12px 16px !important;
    }

    .col-lg-4, .col-lg-8 {
        margin-bottom: 1rem;
    }

    .dashboard-card {
        min-height: auto !important;
    }
}
</style>
@endsection

@section('content')
<div class="container">

   
    <!-- Main Dashboard Content -->
    <div class="row g-3 main-dashboard-section">
        <!-- Section 1: Preferred Candidate -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white text-center py-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-star-fill me-2"></i>Preferred Candidate
                    </h6>
                </div>
                <div class="card-body p-2 d-flex flex-column">
                    <div class="flex-grow-1 d-flex align-items-center justify-content-center">
                        <div style="position: relative; width: 100%; max-width: 300px; height: 300px; margin: 0 auto;">
                            <canvas id="preferredCandidateChart"></canvas>
                            <div class="position-absolute top-50 start-50 translate-middle text-center">
                                @php
                                    // Find preferred candidate
                                    $preferredCandidate = null;
                                    $preferredVotes = 0;
                                    $preferredPosition = null;

                                    // Find leading candidate
                                    $leadingCandidate = null;
                                    $leadingVotes = 0;
                                    $leadingPosition = null;

                                    // Find both preferred and leading candidates
                                    foreach($positions as $position) {
                                        foreach($position->candidates as $candidate) {
                                            $candidateVotes = $candidate->totalVotes();

                                            // Check if this is a preferred candidate
                                            if ($candidate->isPreferred()) {
                                                $preferredCandidate = $candidate;
                                                $preferredVotes = $candidateVotes;
                                                $preferredPosition = $position;
                                            }

                                            // Check if this is the leading candidate overall
                                            if ($candidateVotes > $leadingVotes) {
                                                $leadingCandidate = $candidate;
                                                $leadingVotes = $candidateVotes;
                                                $leadingPosition = $position;
                                            }
                                        }
                                    }

                                    // Determine if preferred is also leading
                                    $isPreferredLeading = ($preferredCandidate && $leadingCandidate && $preferredCandidate->id === $leadingCandidate->id);

                                    // Calculate percentage for preferred candidate
                                    $totalPositionVotes = 0;
                                    if ($isPreferredLeading) {
                                        // If preferred is leading, calculate against total votes
                                        if ($preferredPosition) {
                                            foreach($preferredPosition->candidates as $c) {
                                                $totalPositionVotes += $c->totalVotes();
                                            }
                                        }
                                        $preferredPercentage = ($totalPositionVotes > 0) ? round(($preferredVotes/$totalPositionVotes * 100), 1) : 0;
                                    } else {
                                        // If not leading, calculate against leading candidate
                                        $totalVotes = $preferredVotes + $leadingVotes;
                                        $preferredPercentage = ($totalVotes > 0) ? round(($preferredVotes/$totalVotes * 100), 1) : 0;
                                    }

                                    // Get position (1st, 2nd, 3rd, etc.)
                                    $candidateRank = 0;
                                    if ($preferredCandidate && $preferredPosition) {
                                        $sortedCandidates = $preferredPosition->candidates->sortByDesc(function($c) {
                                            return $c->totalVotes();
                                        });

                                        foreach($sortedCandidates as $index => $c) {
                                            if ($c->id === $preferredCandidate->id) {
                                                $candidateRank = $index + 1;
                                                break;
                                            }
                                        }
                                    }

                                    // Format rank as 1st, 2nd, 3rd, etc.
                                    $rankSuffix = 'th';
                                    if ($candidateRank % 10 == 1 && $candidateRank % 100 != 11) {
                                        $rankSuffix = 'st';
                                    } elseif ($candidateRank % 10 == 2 && $candidateRank % 100 != 12) {
                                        $rankSuffix = 'nd';
                                    } elseif ($candidateRank % 10 == 3 && $candidateRank % 100 != 13) {
                                        $rankSuffix = 'rd';
                                    }
                                    $rankFormatted = $candidateRank . $rankSuffix;
                                @endphp

                                @if($preferredCandidate)
                                <div class="mb-1">
                                    @if($isPreferredLeading)
                                    <span class="badge bg-success px-1 py-1" style="font-size: 0.65rem;">
                                        <i class="bi bi-trophy-fill me-1"></i>{{ $rankFormatted }}
                                    </span>
                                    @else
                                    <span class="badge bg-primary bg-opacity-20 text-primary px-1 py-1" style="font-size: 0.65rem;">
                                        <i class="bi bi-star-fill me-1"></i>{{ $rankFormatted }}
                                    </span>
                                    @endif
                                </div>
                                <div class="fw-bold" style="font-size: 1.3rem; line-height: 1.2;">{{ $preferredPercentage }}%</div>
                                <div class="text-muted" style="font-size: 0.7rem;">vs {{ $isPreferredLeading ? 'others' : 'leader' }}</div>
                                <div class="mt-1 fw-bold" style="font-size: 0.8rem;">{{ $preferredCandidate->name }}</div>
                                <div class="text-muted" style="font-size: 0.65rem;">{{ $preferredPosition->name }} - {{ number_format($preferredVotes) }} votes</div>
                                @else
                                <div class="text-muted">No preferred candidate set</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 2: Overall Results -->
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white py-4" style="background: linear-gradient(135deg, #4a89dc, #5d9cec);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 fw-bold">
                            <i class="bi bi-trophy-fill me-2"></i>Overall Results
                        </h6>
                        <span class="badge bg-white text-primary px-2 py-1" style="font-size: 0.65rem;">
                            <i class="bi bi-award me-1"></i>Top 4
                        </span>
                    </div>
                </div>

                @php
                    // Get all candidates across all positions
                    $allCandidates = collect();
                    foreach ($positions as $position) {
                        foreach ($position->candidates as $candidate) {
                            $votes = $candidate->totalVotes();
                            $allCandidates->push([
                                'candidate' => $candidate,
                                'position' => $position,
                                'votes' => $votes,
                                'percentage' => ($position->totalVotes() > 0) ? round(($votes/$position->totalVotes() * 100), 1) : 0
                            ]);
                        }
                    }

                    // Sort by votes (descending) and take top 3
                    $topCandidates = $allCandidates->sortByDesc('votes')->take(4);
                    $colors = [
                        ['gradient' => 'linear-gradient(135deg, #FFD700, #FFA500)', 'progress' => 'bg-warning'],
                        ['gradient' => 'linear-gradient(135deg, #C0C0C0, #A9A9A9)', 'progress' => 'bg-secondary'],
                        ['gradient' => 'linear-gradient(135deg, #CD7F32, #8B4513)', 'progress' => 'bg-dark']
                    ];
                @endphp

                <div class="card-body p-2" style="max-height: 320px; overflow-y: auto;">
                    @foreach ($topCandidates as $index => $item)
                    <div class="d-flex align-items-center py-1 px-2 mb-2 rounded hover-item" style="transition: all 0.2s ease; background: rgba(0,0,0,0.02);">
                        <div class="me-2">
                             <span class="badge rounded-circle d-flex align-items-center justify-content-center"
                                   style="background: {{ $colors[min($index, 2)]['gradient'] }}; width: 20px; height: 20px; color: white; font-size: 0.7rem; font-weight: bold;">
                                 {{ $loop->iteration }}
                             </span>
                        </div>

                        <div class="flex-grow-1">
                            @php
                                // Get candidate initials (up to 2 characters)
                                $nameParts = explode(' ', $item['candidate']->name);
                                $initials = '';
                                if (count($nameParts) >= 2) {
                                    $initials = strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1));
                                } else {
                                    $initials = strtoupper(substr($item['candidate']->name, 0, 2));
                                }
                            @endphp

                            <div class="d-flex align-items-center mb-1">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-2"
                                     style="width: 24px; height: 24px; background: {{ $colors[min($index, 2)]['gradient'] }}; color: white; font-weight: bold; font-size: 0.7rem;">
                                    {{ $initials }}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.8rem;">{{ $item['candidate']->name }}</div>
                                            <div class="text-muted" style="font-size: 0.65rem;">{{ $item['position']->name }}</div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold" style="font-size: 0.8rem;">{{ number_format($item['votes']) }}</div>
                                            <div class="badge rounded-pill" style="background: {{ $colors[min($index, 2)]['gradient'] }}; font-size: 0.6rem;">
                                                {{ $item['percentage'] }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress" style="height: 4px; border-radius: 2px; background-color: rgba(0,0,0,0.05);">
                                <div class="progress-bar {{ $colors[min($index, 2)]['progress'] }}" role="progressbar"
                                     style="width: {{ $item['percentage'] }}%; border-radius: 2px;"
                                     aria-valuenow="{{ $item['percentage'] }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Section 3: Preferred Candidates Monitoring -->
        <div class="col-lg-4 col-md-12 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white py-4" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 fw-bold">
                            <i class="bi bi-star-fill me-2"></i>Monitoring
                        </h6>
                        <a href="{{ route('monitoring.index') }}" class="btn btn-sm btn-outline-primary" style="font-size: 0.65rem;">
                            <i class="bi bi-gear"></i> Manage
                        </a>
                    </div>
                </div>

                @if(count($preferredCandidates) > 0)

                <div class="card-body p-2" style="max-height: 320px; overflow-y: auto;">
                    @foreach($preferredCandidates as $candidate)
                        @php
                            $gapInfo = $monitoringData[$candidate->id] ?? null;
                            if (!$gapInfo) continue;

                            $isLeading = $gapInfo['is_leading'];
                            $gap = $gapInfo['gap'];
                            $competitor = $gapInfo['competitor'];
                            $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
                            $isAlertTriggered = $alertThreshold && $gap < $alertThreshold;

                            // Calculate percentage for visual representation
                            $totalVotes = $gapInfo['preferred_votes'] + $gapInfo['competitor_votes'];
                            $preferredPercentage = $totalVotes > 0 ? ($gapInfo['preferred_votes'] / $totalVotes) * 100 : 50;

                            // Determine status colors and icons
                            $statusColor = $isLeading ? 'success' : 'danger';
                            $statusIcon = $isLeading ? 'bi-arrow-up-circle-fill' : 'bi-arrow-down-circle-fill';
                            $alertIcon = $isAlertTriggered ? 'bi-exclamation-triangle-fill' : 'bi-shield-check';
                            $alertColor = $isAlertTriggered ? 'danger' : 'success';
                        @endphp
                        <div class="mb-2 p-2 rounded" style="background: rgba(0,0,0,0.02); border-left: 3px solid var(--bs-{{ $statusColor }});">
                            <!-- Ultra Compact Header -->
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="d-flex align-items-center">
                                    <i class="bi {{ $statusIcon }} text-{{ $statusColor }} me-1" style="font-size: 1rem;"></i>
                                    <div>
                                        <div class="fw-bold" style="font-size: 0.8rem;">{{ $candidate->name }}</div>
                                        <div class="d-flex align-items-center">
                                            <small class="text-muted" style="font-size: 0.65rem;">{{ $candidate->position->name }}</small>
                                            <span class="badge bg-{{ $statusColor }} ms-1" style="font-size: 0.6rem;">
                                                @if($isLeading)
                                                    1st
                                                @else
                                                    {{ $gapInfo['preferred_position'] ?? 'N/A' }}{{ $gapInfo['preferred_position'] == 2 ? 'nd' : ($gapInfo['preferred_position'] == 3 ? 'rd' : 'th') }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <a href="{{ route('monitoring.compare', $candidate) }}" class="btn btn-sm btn-outline-{{ $statusColor }}" style="font-size: 0.65rem; padding: 2px 6px;">
                                    <i class="bi bi-bar-chart-line"></i>
                                </a>
                            </div>

                            <!-- Ultra Compact Vote Information -->
                            <div class="row mb-1">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-muted" style="font-size: 0.65rem;">Yours</div>
                                        <div class="fw-bold" style="font-size: 0.85rem;">{{ number_format($gapInfo['preferred_votes']) }}</div>
                                    </div>
                                </div>
                                @if($competitor)
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-muted" style="font-size: 0.65rem;">{{ $isLeading ? '2nd' : 'Leader' }}</div>
                                        <div class="fw-bold" style="font-size: 0.85rem;">{{ number_format($gapInfo['competitor_votes']) }}</div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Ultra Compact Gap Information -->
                            @if($competitor)
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="d-flex align-items-center">
                                    <i class="bi {{ $alertIcon }} text-{{ $alertColor }} me-1" style="font-size: 0.7rem;"></i>
                                    <span class="text-{{ $statusColor }}" style="font-size: 0.7rem;">
                                        {{ $isLeading ? 'Leading by' : 'Behind by' }}
                                    </span>
                                </div>
                                <div class="fw-bold text-{{ $isAlertTriggered ? 'danger' : $statusColor }}" style="font-size: 0.8rem;">
                                    {{ number_format($gap) }}
                                    @if($isAlertTriggered)
                                        <span class="badge bg-danger ms-1" style="font-size: 0.55rem;">!</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Ultra Compact Progress Bar -->
                            <div class="progress" style="height: 4px; border-radius: 2px; background-color: rgba(0,0,0,0.05);">
                                <div class="progress-bar bg-{{ $statusColor }}" role="progressbar"
                                     style="width: {{ $preferredPercentage }}%; border-radius: 2px;"
                                     aria-valuenow="{{ $preferredPercentage }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            @endif
                        </div>
                    @endforeach
                </div>
                @else
                <div class="card-body p-2 d-flex align-items-center justify-content-center" style="min-height: 320px;">
                    <div class="text-center">
                        <i class="bi bi-star text-muted mb-2" style="font-size: 2rem;"></i>
                        <div class="text-muted mb-2" style="font-size: 0.8rem;">No candidates selected</div>
                        <a href="{{ route('monitoring.index') }}" class="btn btn-sm btn-outline-primary" style="font-size: 0.7rem;">
                            <i class="bi bi-plus-circle me-1"></i>Add
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    

    <!-- Polling Stations Section -->
    <div class="row mt-4 mb-3">
        <div class="col-12">
            <div class="section-header d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                    <div class="section-icon-container me-2" style="background-color: rgba(13, 110, 253, 0.1); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                        <i class="bi bi-building text-primary" style="font-size: 1.2rem;"></i>
                    </div>
                    <h4 class="section-title mb-0 fw-bold">Polling Stations</h4>
                    {{-- <span class="badge bg-primary ms-2">{{ count($polling_stations) }}</span> --}}
                </div>

                <!-- Candidate Selector for Win/Loss Analysis -->
                <div class="d-flex align-items-center">
                    <label for="candidateSelector" class="me-2 mb-0 fw-bold">Analyze Candidate:</label>
                    <select id="candidateSelector" class="form-select form-select-sm" style="width: 200px;">
                        <option value="">Select Candidate</option>
                        @foreach($positions as $position)
                            <optgroup label="{{ $position->name }}">
                                @foreach($position->candidates as $candidate)
                                    <option value="{{ $candidate->id }}" data-position="{{ $position->id }}">
                                        {{ $candidate->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-body">
                    <!-- Enhanced Filters Form -->
                    <form method="GET" action="{{ route('home') }}" id="filtersForm" class="mb-4">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-3">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-search me-1"></i>Search
                                </label>
                                <input type="text" name="search" class="form-control form-control-sm"
                                       placeholder="Search stations, locations..."
                                       value="{{ $currentFilters['search'] ?? '' }}">
                            </div>

                            <!-- District Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-geo-alt me-1"></i>District
                                </label>
                                <select name="district" class="form-select form-select-sm" id="districtFilter">
                                    <option value="">All Districts</option>
                                    @foreach($districts as $district)
                                        <option value="{{ $district }}" {{ ($currentFilters['district'] ?? '') == $district ? 'selected' : '' }}>
                                            {{ $district }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- County Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-map me-1"></i>County
                                </label>
                                <select name="county" class="form-select form-select-sm" id="countyFilter">
                                    <option value="">All Counties</option>
                                    @foreach($counties as $county)
                                        <option value="{{ $county }}" {{ ($currentFilters['county'] ?? '') == $county ? 'selected' : '' }}>
                                            {{ $county }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Subcounty Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-building me-1"></i>Subcounty
                                </label>
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($subcounties as $subcounty)
                                        <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Coordinates Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-geo me-1"></i>Coordinates
                                </label>
                                <select name="coordinates" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="with" {{ ($currentFilters['coordinates'] ?? '') == 'with' ? 'selected' : '' }}>With Coordinates</option>
                                    <option value="without" {{ ($currentFilters['coordinates'] ?? '') == 'without' ? 'selected' : '' }}>Without Coordinates</option>
                                </select>
                            </div>

                            <!-- Agents Filter -->
                            <div class="col-md-1">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-people me-1"></i>Agents
                                </label>
                                <select name="agents" class="form-select form-select-sm">
                                    <option value="">All</option>
                                    <option value="with" {{ ($currentFilters['agents'] ?? '') == 'with' ? 'selected' : '' }}>With</option>
                                    <option value="without" {{ ($currentFilters['agents'] ?? '') == 'without' ? 'selected' : '' }}>Without</option>
                                </select>
                            </div>
                        </div>

                        <div class="row g-3 mt-2">
                            <!-- Results Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-check-circle me-1"></i>Results
                                </label>
                                <select name="results" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="submitted" {{ ($currentFilters['results'] ?? '') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                                    <option value="pending" {{ ($currentFilters['results'] ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                                </select>
                            </div>

                            <!-- Evidence Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-file-earmark me-1"></i>Evidence
                                </label>
                                <select name="evidence" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="with" {{ ($currentFilters['evidence'] ?? '') == 'with' ? 'selected' : '' }}>With Evidence</option>
                                    <option value="without" {{ ($currentFilters['evidence'] ?? '') == 'without' ? 'selected' : '' }}>Without Evidence</option>
                                </select>
                            </div>

                            <!-- Per Page -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-list me-1"></i>Per Page
                                </label>
                                <select name="per_page" class="form-select form-select-sm">
                                    <option value="10" {{ ($currentFilters['per_page'] ?? 15) == 10 ? 'selected' : '' }}>10</option>
                                    <option value="15" {{ ($currentFilters['per_page'] ?? 15) == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ ($currentFilters['per_page'] ?? 15) == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ ($currentFilters['per_page'] ?? 15) == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ ($currentFilters['per_page'] ?? 15) == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-6 d-flex align-items-end gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel me-1"></i>Apply Filters
                                </button>
                                <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle me-1"></i>Clear All
                                </a>
                                <div class="ms-auto">
                                    <span class="badge bg-info">
                                        {{ $polling_stations->total() }} stations found
                                    </span>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Polling Stations Table -->
                    <div class="table-responsive polling-stations-table-container">
                        <table class="table table-hover table-striped align-middle" id="pollingStationsTable">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;" class="text-center">#</th>
                                    <th>Station Name</th>
                                    <th>Agent</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                    <th class="text-center">Evidence</th>
                                    <th class="text-center">Votes</th>
                                    <th class="text-center d-none" id="resultHeader">Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($polling_stations as $index => $polling_station)
                                @php
                                    // Check if agent has submitted any votes and get the latest submission time
                                    $latestVote = null;
                                    $hasSubmittedResults = false;
                                    $stationSpoiledVotes = 0;
                                    $evidenceCount = 0;
                                    $totalStationVotes = 0;
                                    
                                    if($polling_station->agent) {
                                        $latestVote = \App\Models\Vote::where('agent_id', $polling_station->agent->id)
                                            ->orderBy('created_at', 'desc')
                                            ->first();
                                        $hasSubmittedResults = $latestVote !== null;

                                        // Get latest 2 evidence records for this agent
                                        $latestEvidence = \App\Models\Eveidence::where('agent_id', $polling_station->agent->id)
                                            ->latest('created_at')
                                            ->limit(2)
                                            ->get();
                                        $evidenceCount = $latestEvidence->count();

                                        // Calculate total votes for this station across all positions
                                        foreach ($positions as $position) {
                                            $totalStationVotes += $position->totalStationVotes($polling_station->id);
                                        }
                                    }
                                    
                                    // Get spoiled votes for this polling station
                                    $stationSpoiledVotes = isset($spoiledVotesByStation[$polling_station->id]) ? $spoiledVotesByStation[$polling_station->id]->total : 0;
                                @endphp
                                <tr class="station-row" 
                                    data-has-evidence="{{ $evidenceCount > 0 ? 'yes' : 'no' }}"
                                    data-has-results="{{ $hasSubmittedResults ? 'yes' : 'no' }}"
                                    data-station-id="{{ $polling_station->id }}"
                                    data-station-name="{{ $polling_station->name }}">
                                    <td class="text-center">{{ ($polling_stations->currentPage() - 1) * $polling_stations->perPage() + $index + 1 }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="station-icon me-2 rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 36px; height: 36px; background-color: rgba(13, 110, 253, 0.1);">
                                                <i class="bi bi-building text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $polling_station->name }}</div>
                                                <div class="small text-muted">Code: {{ $polling_station->code ?? 'N/A' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    
                                    <td>
                                        @if($polling_station->agent)
                                        <div class="d-flex align-items-center">
                                            <div class="agent-icon me-2 rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 32px; height: 32px; background-color: rgba(108, 117, 125, 0.1);">
                                                <i class="bi bi-person text-secondary"></i>
                                            </div>
                                            <div>
                                                <div>{{ $polling_station->agent->user->name }}</div>
                                                <div class="small text-muted">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    {{ $polling_station->agent->user->phone_number }}
                                                </div>
                                            </div>
                                        </div>
                                        @else
                                        <span class="text-muted">No agent assigned</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($polling_station->agent)
                                            @if($hasSubmittedResults)
                                                <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Results Submitted</span>
                                            @else
                                                <span class="badge bg-warning text-dark"><i class="bi bi-exclamation-triangle me-1"></i> Results Pending</span>
                                            @endif
                                            
                                            @if($stationSpoiledVotes > 0)
                                                <div class="mt-1">
                                                    <span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i> {{ $stationSpoiledVotes }} Spoiled</span>
                                                </div>
                                            @endif
                                        @else
                                            <span class="badge bg-secondary"><i class="bi bi-dash-circle me-1"></i> No Agent</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($latestVote)
                                            {{ $latestVote->created_at->format('M d, H:i') }}
                                        @else
                                            <span class="text-muted">--</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($polling_station->agent && $evidenceCount > 0)
                                            <div class="evidence-container">
                                                <div class="d-flex align-items-center justify-content-center mb-2">
                                                    <span class="badge bg-info me-2">{{ $evidenceCount }} Evidence</span>
                                                    <button class="btn btn-sm btn-primary view-evidence-btn"
                                                            data-station-id="{{ $polling_station->id }}"
                                                            data-agent-id="{{ $polling_station->agent->id }}"
                                                            title="View All Evidence">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>

                                                <!-- Latest Evidence Preview -->
                                                <div class="latest-evidence-preview">
                                                    @foreach($latestEvidence as $index => $evidence)
                                                        <div class="evidence-item mb-1 p-2 border rounded" style="font-size: 0.85rem;">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="evidence-info flex-grow-1">
                                                                    <div class="fw-bold text-truncate" style="max-width: 120px;" title="{{ $evidence->file_name ?? 'Evidence File' }}">
                                                                        {{ $evidence->file_name ?? 'Evidence File' }}
                                                                    </div>
                                                                    <div class="small text-muted">
                                                                        {{ $evidence->created_at->format('M d, H:i') }}
                                                                        @if($index === 0)
                                                                            <span class="badge badge-sm bg-success ms-1">Latest</span>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                                <div class="evidence-actions">
                                                                    <a href="{{ url('files/' . $evidence->file_url) }}"
                                                                       target="_blank"
                                                                       class="btn btn-xs btn-outline-primary"
                                                                       title="View {{ $evidence->file_name ?? 'Evidence' }}">
                                                                        <i class="bi bi-eye" style="font-size: 0.7rem;"></i>
                                                                    </a>
                                                                    <a href="{{ url('files/' . $evidence->file_url) }}"
                                                                       download
                                                                       class="btn btn-xs btn-outline-success ms-1"
                                                                       title="Download {{ $evidence->file_name ?? 'Evidence' }}">
                                                                        <i class="bi bi-download" style="font-size: 0.7rem;"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">No Evidence</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($totalStationVotes > 0)
                                            <span class="fw-bold">{{ number_format($totalStationVotes) }}</span>
                                        @else
                                            <span class="text-muted">--</span>
                                        @endif
                                    </td>
                                    <td class="text-center d-none station-result-cell" data-station-id="{{ $polling_station->id }}">
                                        <span class="result-badge d-none"></span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="showing-entries">
                            <small class="text-muted">
                                Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                                of {{ $polling_stations->total() }} stations
                            </small>
                        </div>
                        <div class="pagination-container">
                            {{ $polling_stations->links('pagination::bootstrap-4') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Station Details Modal -->
    <div class="modal fade" id="stationDetailsModal" tabindex="-1" aria-labelledby="stationDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="stationDetailsModalLabel">Polling Station Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="stationDetailsContent">
                    <!-- Station details will be loaded here dynamically -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading station details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Simple Vote Trends Graph Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="section-header d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                    <div class="section-icon-container me-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 12px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                        <i class="bi bi-graph-up text-white" style="font-size: 1.2rem;"></i>
                    </div>
                    <div>
                        <h4 class="section-title mb-0 fw-bold">Vote Trends Over Time</h4>
                        <small class="text-muted">Track candidate vote counts throughout the day</small>
                    </div>
                </div>

                <!-- Simple Controls -->
                <div class="d-flex align-items-center gap-2">
                    <select class="form-select form-select-sm" id="positionFilter" style="width: auto;">
                        <option value="overall">All Positions</option>
                        @foreach($positions as $position)
                            <option value="{{ $position->id }}">{{ $position->name }}</option>
                        @endforeach
                    </select>

                    <button class="btn btn-outline-secondary btn-sm" id="refreshChart" title="Refresh Data">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>

            <div class="card bg-white rounded shadow-sm border-0" style="box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;">
                <div class="card-body p-4">
                    <!-- Simple Vote Trends Chart -->
                    <div class="vote-trends-chart">
                        <div class="chart-container" style="position: relative; height: 400px; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 8px; padding: 10px;">
                            <canvas id="voteTrendsChart"></canvas>
                        </div>
                    </div>
                        

                    <!-- Simple Chart Summary -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0 fw-bold">
                                            <i class="bi bi-info-circle text-primary me-1"></i>
                                            Chart Information
                                        </h6>
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-success">Live</span>
                                            <small class="text-muted" id="lastUpdated">Updated: Just now</small>
                                        </div>
                                    </div>

                                    <!-- Simple Stats Row -->
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-clock text-primary mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">{{ date('H:i') }}</div>
                                                <small class="text-muted">Current Time</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-people text-success mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold" id="totalVotes">{{ number_format(\App\Models\Vote::sum('number_of_votes')) }}</div>
                                                <small class="text-muted">Total Votes</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-geo-alt text-info mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">{{ $polling_stations->count() }}</div>
                                                <small class="text-muted">Stations</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-graph-up text-warning mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">Real-time</div>
                                                <small class="text-muted">Updates</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @include('partials.home.map')
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
</div>

@endsection

@section('styles')

<link rel="stylesheet" href="{{ asset('css/custom.css') }}">

@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- Polling Stations Table Logic ---
        const table = document.getElementById('pollingStationsTable');
        const tableBody = table.querySelector('tbody');
        const stationRows = Array.from(document.querySelectorAll('.station-row'));
        const resultHeader = document.getElementById('resultHeader');
        const rowsPerPage = 10;
        let currentPage = 1;

        // Filter controls
        const searchInput = document.getElementById('stationSearchInput');
        const filterSelect = document.getElementById('stationFilterSelect');
        const candidateFilter = document.getElementById('candidateTableFilter');
        const candidateSelector = document.getElementById('candidateSelector');
        const pagination = document.getElementById('stations-pagination');

        // Precompute votes by station and candidate for fast lookup
        const stationVotes = {};
        @foreach ($polling_stations as $polling_station)
            stationVotes[{{ $polling_station->id }}] = {};
            @foreach ($positions as $position)
                @foreach ($position->candidates as $candidate)
                    stationVotes[{{ $polling_station->id }}][{{ $candidate->id }}] = {{ \App\Models\Vote::join('agents', 'votes.agent_id', '=', 'agents.id')
                        ->where('agents.polling_station_id', $polling_station->id)
                        ->where('votes.candidate_id', $candidate->id)
                        ->sum('votes.number_of_votes') ?? 0 }};
                @endforeach
            @endforeach
        @endforeach

        // Debug mode - can be enabled for troubleshooting
        const debugMode = false;
        if (debugMode) {
            console.log('Station votes data loaded:', stationVotes);
            console.log('Candidate selector element:', document.getElementById('candidateSelector'));
            console.log('Result header element:', document.getElementById('resultHeader'));
            console.log('Result cells:', document.querySelectorAll('.station-result-cell').length);
        }

        function getFilteredRows() {
            let filtered = stationRows;
            // Candidate filter (legacy system - only if candidateFilter exists and has a value)
            if (candidateFilter && candidateFilter.value && candidateFilter.value !== 'all') {
                const candidateVal = candidateFilter.value;
                const resultHeader = document.getElementById('resultHeader');
                if (resultHeader) {
                    resultHeader.classList.remove('d-none');
                }
                const candidateId = parseInt(candidateVal.replace('candidate-', ''));
                filtered = filtered.filter(row => {
                    const stationId = row.getAttribute('data-station-id');
                    // Only show stations where this candidate has votes
                    return stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined;
                });
            } else if (candidateFilter) {
                const resultHeader = document.getElementById('resultHeader');
                if (resultHeader) {
                    resultHeader.classList.add('d-none');
                }
            }
            // Search filter
            if (searchInput && searchInput.value) {
                const searchTerm = searchInput.value.trim().toLowerCase();
                if (searchTerm) {
                    filtered = filtered.filter(row =>
                        row.getAttribute('data-station-name').toLowerCase().includes(searchTerm) ||
                        row.textContent.toLowerCase().includes(searchTerm)
                    );
                }
            }
            // Evidence/results filter
            if (filterSelect && filterSelect.value) {
                const filterValue = filterSelect.value;
                if (filterValue !== 'all') {
                    filtered = filtered.filter(row => {
                        if (filterValue === 'with-evidence') return row.getAttribute('data-has-evidence') === 'yes';
                        if (filterValue === 'without-evidence') return row.getAttribute('data-has-evidence') !== 'yes';
                        if (filterValue === 'complete') return row.getAttribute('data-has-results') === 'yes';
                        if (filterValue === 'incomplete') return row.getAttribute('data-has-results') !== 'yes';
                        return true;
                    });
                }
            }
            // Position filter (not implemented per station, could be added if needed)
            // ...
            return filtered;
        }

        function renderTable() {
            const candidateVal = (candidateFilter && candidateFilter.value) ? candidateFilter.value : 'all';
            const candidateId = candidateVal !== 'all' ? candidateVal.replace('candidate-', '') : null;
            const filteredRows = getFilteredRows();
            const total = filteredRows.length;
            const totalPages = Math.max(1, Math.ceil(total / rowsPerPage));
            currentPage = Math.min(currentPage, totalPages);
            // Hide all rows
            stationRows.forEach(r => r.classList.add('d-none'));
            // Show only rows for current page
            const start = (currentPage - 1) * rowsPerPage;
            const end = start + rowsPerPage;
            filteredRows.slice(start, end).forEach(r => r.classList.remove('d-none'));
            
            // Update candidate results summary
            const candidateResultsSummary = document.getElementById('candidateResultsSummary');
            if (candidateId) {
                // Show the summary and update counts
                let wonCount = 0;
                let lostCount = 0;
                
                // Count won/lost across all stations for this candidate
                filteredRows.forEach(row => {
                    const stationId = row.getAttribute('data-station-id');
                    if (stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined) {
                        const votes = stationVotes[stationId][candidateId] || 0;
                        const stationVotesArr = Object.values(stationVotes[stationId]);
                        const maxVotes = Math.max(...stationVotesArr);

                        if (votes === maxVotes && maxVotes > 0) {
                            wonCount++;
                        } else if (votes > 0) {
                            lostCount++;
                        }
                    }
                });
                
                // Update the candidate name and position (only if candidateFilter exists)
                if (candidateFilter && candidateFilter.options && candidateFilter.selectedIndex >= 0) {
                    const selectedOption = candidateFilter.options[candidateFilter.selectedIndex];
                    const candidateNameEl = document.getElementById('candidateName');
                    const candidatePositionEl = document.getElementById('candidatePosition');
                    if (candidateNameEl && candidatePositionEl) {
                        candidateNameEl.textContent = selectedOption.textContent.split(' (')[0];
                        candidatePositionEl.textContent = '(' + selectedOption.textContent.split(' (')[1];
                    }
                }
                
                // Update the counts (with null checks)
                const wonCountEl = document.getElementById('wonCount');
                const lostCountEl = document.getElementById('lostCount');
                if (wonCountEl) wonCountEl.textContent = wonCount;
                if (lostCountEl) lostCountEl.textContent = lostCount;

                // Show the summary (with null check)
                const candidateResultsSummary = document.getElementById('candidateResultsSummary');
                if (candidateResultsSummary) candidateResultsSummary.classList.remove('d-none');
            } else {
                // Hide the summary when no candidate is selected (with null check)
                const candidateResultsSummary = document.getElementById('candidateResultsSummary');
                if (candidateResultsSummary) candidateResultsSummary.classList.add('d-none');
            }
            // Show/hide and update Result column
            stationRows.forEach(row => {
                const cell = row.querySelector('.station-result-cell');
                if (!cell) return;
                cell.classList.add('d-none');
                cell.innerHTML = '';
                if (candidateId && !row.classList.contains('d-none')) {
                    const stationId = row.getAttribute('data-station-id');
                    if (stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined) {
                        const votes = stationVotes[stationId][candidateId] || 0;
                        const stationVotesArr = Object.values(stationVotes[stationId]);
                        const maxVotes = Math.max(...stationVotesArr);
                        cell.classList.remove('d-none');
                        if (votes === maxVotes && maxVotes > 0) {
                            cell.innerHTML = '<span class="badge bg-success">Won</span>';
                        } else {
                            cell.innerHTML = '<span class="badge bg-danger">Lost</span>';
                        }
                    }
                }
            });
            // Update pagination
            renderPagination(totalPages);
            // Update showing info (with null checks)
            const showingStart = document.getElementById('showing-start');
            const showingEnd = document.getElementById('showing-end');
            const totalEntries = document.getElementById('total-entries');

            if (showingStart) showingStart.textContent = total === 0 ? 0 : (start + 1);
            if (showingEnd) showingEnd.textContent = Math.min(end, total);
            if (totalEntries) totalEntries.textContent = total;
            // Show/hide no-results row
            let noRow = document.getElementById('noStationsMessage');
            if (total === 0) {
                if (!noRow) {
                    noRow = document.createElement('tr');
                    noRow.id = 'noStationsMessage';
                    noRow.innerHTML = `<td colspan="8" class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-search text-muted mb-3" style="font-size: 2rem;"></i>
                            <h5>No polling stations found</h5>
                            <p class="text-muted">Try adjusting your search or filter criteria</p>
                            <button class="btn btn-sm btn-outline-primary" id="resetFiltersBtn"><i class="bi bi-arrow-counterclockwise"></i> Reset Filters</button>
                        </div>
                    </td>`;
                    tableBody.appendChild(noRow);
                }
            } else if (noRow) {
                noRow.remove();
            }
        }

        function renderPagination(totalPages) {
            if (!pagination) return; // Exit early if pagination element doesn't exist

            // Remove old page links except arrows
            pagination.querySelectorAll('li.page-item.page-num').forEach(e => e.remove());
            // Insert numbered page links
            const nextBtn = pagination.querySelector('li:last-child');
            if (nextBtn) {
                for (let i = 1; i <= totalPages; i++) {
                    const li = document.createElement('li');
                    li.className = 'page-item page-num' + (i === currentPage ? ' active' : '');
                    const a = document.createElement('a');
                    a.className = 'page-link';
                    a.href = '#';
                    a.textContent = i;
                    a.dataset.page = i;
                    li.appendChild(a);
                    pagination.insertBefore(li, nextBtn);
                }
            }
            // Enable/disable prev/next (with null checks)
            const firstChild = pagination.querySelector('li:first-child');
            const lastChild = pagination.querySelector('li:last-child');
            if (firstChild) firstChild.classList.toggle('disabled', currentPage === 1);
            if (lastChild) lastChild.classList.toggle('disabled', currentPage === totalPages);
        }

        // Pagination click (with null check)
        if (pagination) {
            pagination.addEventListener('click', function(e) {
                const link = e.target.closest('a.page-link');
                if (!link) return;
                e.preventDefault();
                if (link.dataset.page) {
                    currentPage = parseInt(link.dataset.page);
                    renderTable();
                } else if (link.parentNode === pagination.querySelector('li:first-child')) {
                    if (currentPage > 1) { currentPage--; renderTable(); }
                } else if (link.parentNode === pagination.querySelector('li:last-child')) {
                    const filteredRows = getFilteredRows();
                    const totalPages = Math.max(1, Math.ceil(filteredRows.length / rowsPerPage));
                    if (currentPage < totalPages) { currentPage++; renderTable(); }
                }
            });
        }

        // Event listeners for filters (with null checks)
        if (searchInput) searchInput.addEventListener('input', () => { currentPage = 1; renderTable(); });
        if (filterSelect) filterSelect.addEventListener('change', () => { currentPage = 1; renderTable(); });
        if (candidateFilter) candidateFilter.addEventListener('change', () => { currentPage = 1; renderTable(); });
        
        // Reset filters button
        document.body.addEventListener('click', (e) => {
            if (e.target && e.target.id === 'resetFiltersBtn') {
                if (searchInput) searchInput.value = '';
                if (filterSelect) filterSelect.value = 'all';
                if (candidateFilter) candidateFilter.value = 'all';
                const candidateResultsSummary = document.getElementById('candidateResultsSummary');
                if (candidateResultsSummary) candidateResultsSummary.classList.add('d-none');
                currentPage = 1;
                renderTable();
            }
        });

        // Initial render
        renderTable();

        // --- Candidate Selector Logic ---
        function initializeCandidateSelector() {
            const candidateSelector = document.getElementById('candidateSelector');
            if (candidateSelector) {
                if (debugMode) console.log('Candidate selector found, adding event listener');
                candidateSelector.addEventListener('change', function() {
                    const selectedCandidateId = this.value;
                    if (debugMode) console.log('Candidate selected:', selectedCandidateId);

                    const resultHeader = document.getElementById('resultHeader');
                    const resultCells = document.querySelectorAll('.station-result-cell');

                    if (debugMode) {
                        console.log('Result header found:', !!resultHeader);
                        console.log('Result cells found:', resultCells.length);
                    }

                    if (selectedCandidateId) {
                        // Show result column
                        if (resultHeader) {
                            resultHeader.classList.remove('d-none');
                            // Update result header with candidate name
                            const selectedOption = this.options[this.selectedIndex];
                            resultHeader.textContent = `${selectedOption.textContent} Result`;
                        }

                        resultCells.forEach(cell => cell.classList.remove('d-none'));

                        // Calculate and display win/loss for each station
                        updateStationResults(selectedCandidateId);
                    } else {
                        // Hide result column
                        if (resultHeader) {
                            resultHeader.classList.add('d-none');
                        }
                        resultCells.forEach(cell => cell.classList.add('d-none'));
                    }
                });
                return true;
            } else {
                if (debugMode) console.error('Candidate selector not found!');
                return false;
            }
        }

        // Try to initialize immediately, and retry if needed
        if (!initializeCandidateSelector()) {
            // Retry after a short delay in case of timing issues
            setTimeout(initializeCandidateSelector, 100);
        }

        // Add a test function to verify functionality
        window.testCandidateSelector = function() {
            const selector = document.getElementById('candidateSelector');
            const resultHeader = document.getElementById('resultHeader');
            const resultCells = document.querySelectorAll('.station-result-cell');

            console.log('=== Candidate Selector Test ===');
            console.log('Selector found:', !!selector);
            console.log('Result header found:', !!resultHeader);
            console.log('Result cells found:', resultCells.length);
            console.log('Station votes data:', Object.keys(stationVotes).length, 'stations');

            if (selector && selector.options.length > 1) {
                console.log('Available candidates:', Array.from(selector.options).slice(1).map(opt => opt.textContent));
            }

            return {
                selector: !!selector,
                resultHeader: !!resultHeader,
                resultCells: resultCells.length,
                stationCount: Object.keys(stationVotes).length
            };
        };

        function updateStationResults(candidateId) {
            if (debugMode) {
                console.log('Updating station results for candidate:', candidateId);
                console.log('Station votes data:', stationVotes);
            }

            const resultCells = document.querySelectorAll('.station-result-cell');
            if (debugMode) console.log('Processing', resultCells.length, 'result cells');

            resultCells.forEach((cell, index) => {
                const stationId = cell.dataset.stationId;
                let resultBadge = cell.querySelector('.result-badge');

                // Create result badge if it doesn't exist
                if (!resultBadge) {
                    resultBadge = document.createElement('span');
                    resultBadge.className = 'result-badge d-none';
                    cell.appendChild(resultBadge);
                }

                if (debugMode) console.log(`Processing station ${stationId}, badge found:`, !!resultBadge);

                if (stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined) {
                    const candidateVotes = stationVotes[stationId][candidateId];
                    if (debugMode) console.log(`Station ${stationId}: candidate has ${candidateVotes} votes`);

                    // Find the highest vote count for this station
                    let maxVotes = 0;
                    let isWinner = false;

                    Object.values(stationVotes[stationId]).forEach(votes => {
                        if (votes > maxVotes) {
                            maxVotes = votes;
                        }
                    });

                    isWinner = candidateVotes === maxVotes && candidateVotes > 0;
                    if (debugMode) console.log(`Station ${stationId}: max votes ${maxVotes}, is winner: ${isWinner}`);

                    // Update the result badge
                    if (candidateVotes > 0) {
                        if (isWinner) {
                            resultBadge.className = 'badge bg-success result-badge';
                            resultBadge.innerHTML = '<i class="bi bi-trophy-fill me-1"></i>Won';
                        } else {
                            resultBadge.className = 'badge bg-danger result-badge';
                            resultBadge.innerHTML = '<i class="bi bi-x-circle-fill me-1"></i>Lost';
                        }
                        resultBadge.classList.remove('d-none');
                    } else {
                        resultBadge.className = 'badge bg-secondary result-badge';
                        resultBadge.innerHTML = '<i class="bi bi-dash-circle me-1"></i>No Votes';
                        resultBadge.classList.remove('d-none');
                    }
                } else {
                    if (debugMode) console.log(`Station ${stationId}: no data for candidate ${candidateId}`);
                    resultBadge.className = 'badge bg-secondary result-badge';
                    resultBadge.innerHTML = '<i class="bi bi-question-circle me-1"></i>No Data';
                    resultBadge.classList.remove('d-none');
                }
            });
        }

        // --- Evidence View Logic ---
        document.addEventListener('click', function(e) {
            if (e.target.closest('.view-evidence-btn')) {
                const btn = e.target.closest('.view-evidence-btn');
                const stationId = btn.dataset.stationId;
                const agentId = btn.dataset.agentId;

                viewStationEvidence(stationId, agentId, btn);
            }
        });

        async function viewStationEvidence(stationId, agentId, button) {
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
            button.disabled = true;

            try {
                const response = await fetch(`/api/station-evidence/${stationId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.evidence && data.evidence.length > 0) {
                        // Open each evidence file in a new tab
                        data.evidence.forEach((evidence, index) => {
                            setTimeout(() => {
                                const url = `/files/${evidence.file_url}`;
                                window.open(url, '_blank');
                            }, index * 100); // Small delay between opening tabs to prevent popup blocker
                        });

                        showToast(`Opened ${data.evidence.length} evidence file(s) in new tabs`, 'success');
                    } else {
                        showToast('No evidence files found', 'warning');
                    }
                } else {
                    throw new Error('Failed to fetch evidence');
                }
            } catch (error) {
                console.error('Error viewing evidence:', error);
                showToast('Failed to view evidence', 'error');
            } finally {
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        }

        // --- Evidence Download Logic ---
        document.addEventListener('click', function(e) {
            if (e.target.closest('.download-evidence-btn')) {
                const btn = e.target.closest('.download-evidence-btn');
                const stationId = btn.dataset.stationId;
                const agentId = btn.dataset.agentId;

                downloadStationEvidence(stationId, agentId, btn);
            }
        });

        async function downloadStationEvidence(stationId, agentId, button) {
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
            button.disabled = true;

            try {
                const response = await fetch(`/api/station-evidence/${stationId}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.evidence && data.evidence.length > 0) {
                        // Create and download ZIP file
                        await downloadEvidenceAsZip(data.evidence, data.station_name);

                        // Show success message
                        showToast('Evidence downloaded successfully', 'success');
                    } else {
                        showToast('No evidence files found', 'warning');
                    }
                } else {
                    throw new Error('Failed to fetch evidence');
                }
            } catch (error) {
                console.error('Error downloading evidence:', error);
                showToast('Failed to download evidence', 'error');
            } finally {
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        }

        async function downloadEvidenceAsZip(evidenceFiles, stationName) {
            // For now, download files individually
            // In a production environment, you'd want to create a proper ZIP file server-side

            for (const evidence of evidenceFiles) {
                const link = document.createElement('a');
                link.href = `/files/${evidence.file_url}`;
                link.download = evidence.file_name || `evidence_${evidence.id}`;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Small delay between downloads
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        function showToast(message, type = 'info') {
            // Use the smooth refresh toast if available, otherwise create a simple one
            if (window.smoothRefreshUtils && window.smoothRefreshUtils.showToast) {
                window.smoothRefreshUtils.showToast(message, type);
            } else {
                alert(message); // Fallback
            }
        }

        // --- End Polling Stations Table Logic ---

        // --- Station Cards Filtering Logic ---
        const stationCards = document.querySelectorAll('.station-card');
        const filterButton = document.getElementById('stationFilterButton');
        const positionFilter = document.getElementById('positionFilter');
        
        // Add event listener to filter button (with null check)
        if (filterButton) {
            filterButton.addEventListener('click', filterStations);
        }

        // Add event listener to search input for real-time filtering (with null check)
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                if (this.value.length > 2 || this.value.length === 0) {
                    filterStations();
                }
            });
        }

        // Add event listener to position filter (with null check)
        if (positionFilter) {
            positionFilter.addEventListener('change', function() {
                filterByPosition();
            });
        }
        
        // Function to filter dashboard by position
        function filterByPosition() {
            const selectedPosition = positionFilter ? positionFilter.value : 'all';
            
            // Show/hide results based on position
            if (selectedPosition === 'all') {
                document.querySelectorAll('.results-card-compact').forEach(function(card) {
                    card.closest('.col-12').style.display = '';
                });
            } else {
                const positionId = selectedPosition.replace('position-', '');
                document.querySelectorAll('.results-card-compact').forEach(function(card) {
                    const cardPositionId = card.getAttribute('data-position-id');
                    if (cardPositionId === positionId) {
                        card.closest('.col-12').style.display = '';
                    } else {
                        card.closest('.col-12').style.display = 'none';
                    }
                });
            }
            
            // Filter station cards to show only the selected position data
            stationCards.forEach(function(card) {
                if (selectedPosition === 'all') {
                    // Show all position tables
                    card.querySelectorAll('.position-votes-table').forEach(function(table) {
                        table.closest('.position-section').style.display = '';
                    });
                } else {
                    // Show only selected position tables
                    const positionId = selectedPosition.replace('position-', '');
                    card.querySelectorAll('.position-votes-table').forEach(function(table) {
                        const tablePositionId = table.getAttribute('data-position-id');
                        if (tablePositionId === positionId) {
                            table.closest('.position-section').style.display = '';
                        } else {
                            table.closest('.position-section').style.display = 'none';
                        }
                    });
                }
            });
        }
        
        // Function to filter stations
        function filterStations() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const filterValue = filterSelect ? filterSelect.value : 'all';
            
            stationCards.forEach(function(card) {
                let showCard = true;
                
                // Filter by search term
                if (searchTerm) {
                    const stationName = card.querySelector('.station-name').textContent.toLowerCase();
                    if (!stationName.includes(searchTerm)) {
                        showCard = false;
                    }
                }
                
                // Apply additional filters
                if (showCard && filterValue !== 'all') {
                    switch(filterValue) {
                        case 'with-evidence':
                            if (!card.querySelector('.evidence-list') || card.querySelectorAll('.evidence-item').length === 0) {
                                showCard = false;
                            }
                            break;
                        case 'without-evidence':
                            if (card.querySelector('.evidence-list') && card.querySelectorAll('.evidence-item').length > 0) {
                                showCard = false;
                            }
                            break;
                        case 'complete':
                            // Check if all positions have votes reported
                            const positionTables = card.querySelectorAll('.position-votes-table');
                            let allComplete = true;
                            positionTables.forEach(function(table) {
                                const hasVotes = table.querySelector('tbody tr:not(.no-results)');
                                if (!hasVotes) {
                                    allComplete = false;
                                }
                            });
                            if (!allComplete) {
                                showCard = false;
                            }
                            break;
                        case 'incomplete':
                            // Check if any positions are missing votes
                            const tables = card.querySelectorAll('.position-votes-table');
                            let hasIncomplete = false;
                            tables.forEach(function(table) {
                                const hasVotes = table.querySelector('tbody tr:not(.no-results)');
                                if (!hasVotes) {
                                    hasIncomplete = true;
                                }
                            });
                            if (!hasIncomplete) {
                                showCard = false;
                            }
                            break;
                    }
                }
                
                // Show or hide the card
                if (showCard) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show message if no results
            const visibleCards = document.querySelectorAll('.station-card[style=""]');
            const noResultsMessage = document.getElementById('noStationsMessage');
            
            if (visibleCards.length === 0) {
                // Create message if it doesn't exist
                if (!noResultsMessage) {
                    const message = document.createElement('div');
                    message.id = 'noStationsMessage';
                    message.className = 'col-12 text-center py-5';
                    message.innerHTML = `
                        <div class="empty-state">
                            <i class="bi bi-search text-muted mb-3" style="font-size: 2rem;"></i>
                            <h5>No polling stations found</h5>
                            <p class="text-muted">Try adjusting your search or filter criteria</p>
                            <button class="btn btn-sm filter-btn" onclick="document.getElementById('stationSearchInput').value='';document.getElementById('stationFilterSelect').value='all';filterStations()">
                                <i class="bi bi-arrow-counterclockwise"></i> Reset Filters
                            </button>
                        </div>
                    `;
                    document.querySelector('.row:last-child').appendChild(message);
                }
            } else if (noResultsMessage) {
                // Remove message if results exist
                noResultsMessage.remove();
            }
        }
    });
</script>
    
<!-- Load Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded, initializing chart...');
    
    // Initialize the chart
    const initChart = () => {
        const ctx = document.getElementById('preferredCandidateChart');
        if (!ctx) {
            console.error('Chart element not found');
            return;
        }

        console.log('Initializing chart...');

        // Get data from PHP variables
        @if(isset($preferredCandidate) && isset($leadingCandidate) && $preferredCandidate && $leadingCandidate)
        const preferredVotes = {{ $preferredVotes ?? 0 }};
        const leadingVotes = {{ $leadingVotes ?? 0 }};
        const isPreferredLeading = {{ isset($isPreferredLeading) && $isPreferredLeading ? 'true' : 'false' }};

        // Set labels based on whether preferred is leading
        let firstLabel, secondLabel, firstValue, secondValue;

        if (isPreferredLeading) {
            firstLabel = '{{ $preferredCandidate ? addslashes($preferredCandidate->name) : "Preferred" }}';
            secondLabel = 'Other Candidates';
            firstValue = preferredVotes;
            secondValue = {{ $totalPositionVotes ?? 0 }} - preferredVotes;
        } else {
            firstLabel = '{{ $preferredCandidate ? addslashes($preferredCandidate->name) : "Preferred" }}';
            secondLabel = '{{ $leadingCandidate ? addslashes($leadingCandidate->name) : "Leading" }}';
            firstValue = preferredVotes;
            secondValue = leadingVotes;
        }
        @else
        // Default values if no candidates are found
        let firstLabel = 'No Data';
        let secondLabel = '';
        let firstValue = 1;
        let secondValue = 0;
        const isPreferredLeading = false;
        @endif

        const data = {
            labels: [firstLabel, secondLabel],
            datasets: [{
                data: [firstValue, secondValue],
                backgroundColor: [
                    isPreferredLeading ? 'rgba(40, 167, 69, 0.9)' : 'rgba(13, 110, 253, 0.9)',
                    isPreferredLeading ? 'rgba(108, 117, 125, 0.2)' : 'rgba(40, 167, 69, 0.9)'
                ],
                borderColor: [
                    isPreferredLeading ? 'rgba(40, 167, 69, 1)' : 'rgba(13, 110, 253, 1)',
                    isPreferredLeading ? 'rgba(108, 117, 125, 0.3)' : 'rgba(40, 167, 69, 1)'
                ],
                borderWidth: 2,
                cutout: '75%',
                borderRadius: 4,
                spacing: 2
            }]
        };

        const config = {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: { size: 14, weight: 'bold' },
                        bodyFont: { size: 13 },
                        padding: 12,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                
                                // Use the same percentage calculation as in PHP
                                let percentage;
                                if (context.dataIndex === 0) {
                                    // For preferred candidate, use the same percentage as displayed in center
                                    percentage = {{ isset($preferredPercentage) ? $preferredPercentage : 0 }};
                                } else {
                                    // For other candidate(s), calculate the remaining percentage
                                    percentage = Math.round((value / total) * 100);
                                }
                                
                                return `${label}: ${value.toLocaleString()} votes (${percentage}%)`;
                            }
                        }
                    }
                },
                rotation: -90,
                circumference: 360,
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        };

        try {
            new Chart(ctx, config);
            console.log('Chart initialized successfully');
        } catch (error) {
            console.error('Error initializing chart:', error);
        }
    };

    // Initialize the chart
    initChart();
});
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple Chart System
        let currentChart = null;
        let currentData = null;
        let currentPosition = 'overall';

        // Initialize chart
        initializeChart();

        function initializeChart() {
            const ctx = document.getElementById('voteTrendsChart');
            if (!ctx) return;

            // Create loading indicator
            const loadingIndicator = createLoadingIndicator(ctx);

            // Load initial data
            loadChartData('overall');

            // Setup event listeners
            setupEventListeners();
        }

        function createLoadingIndicator(ctx) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'text-center py-5';
            loadingIndicator.innerHTML = `
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary me-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>
                        <p class="mb-1 fw-bold">Loading ranking data...</p>
                        <small class="text-muted">Analyzing candidate trends</small>
                    </div>
                </div>
            `;
            ctx.parentNode.appendChild(loadingIndicator);
            return loadingIndicator;
        }

        function setupEventListeners() {
            // Position filter
            const positionFilter = document.getElementById('positionFilter');
            if (positionFilter) {
                positionFilter.addEventListener('change', function() {
                    currentPosition = this.value;
                    loadChartData(currentPosition);
                });
            }

            // Refresh button
            const refreshBtn = document.getElementById('refreshChart');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
                    loadChartData(currentPosition);
                    setTimeout(() => {
                        this.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                    }, 1000);
                });
            }
        }

        function loadChartData(positionId) {
            const loadingIndicator = document.querySelector('.text-center.py-5');

            fetch(`/vote-trends-data/${positionId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    currentData = data;

                    // Remove loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    // Create/update chart
                    createVoteTrendsChart(data);
                    updateLastUpdated();

                })
                .catch(error => {
                    console.error('Error fetching vote trends data:', error);
                    if (loadingIndicator) {
                        loadingIndicator.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Failed to load vote trends data. Please try refreshing the page.
                            </div>
                        `;
                    }
                });
        }

        function createVoteTrendsChart(data) {
            const ctx = document.getElementById('voteTrendsChart');
            if (!ctx) return;

            // Destroy existing chart
            if (currentChart) {
                currentChart.destroy();
            }

            // Simple chart configuration
            const config = {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: data.datasets.map((dataset, index) => ({
                        label: dataset.label,
                        data: dataset.data,
                        borderColor: dataset.borderColor,
                        backgroundColor: `rgba(${hexToRgb(dataset.borderColor)}, 0.1)`,
                        borderWidth: 3,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointBorderWidth: 2,
                        pointBorderColor: '#fff',
                        tension: 0.2,
                        fill: false
                    }))
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                },
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            },
                            title: {
                                display: true,
                                text: 'Vote Count',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)',
                                lineWidth: 1
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)',
                                lineWidth: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: 'rgba(255,255,255,0.2)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(context) {
                                    return 'Time: ' + context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw.toLocaleString() + ' votes';
                                }
                            }
                        }
                    }
                }
            };

            currentChart = new Chart(ctx, config);
        }

        function updateLastUpdated() {
            const element = document.getElementById('lastUpdated');
            if (element) {
                element.textContent = `Updated: ${new Date().toLocaleTimeString()}`;
            }
        }

        // Utility functions

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ?
                `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
                '0, 0, 0';
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            loadChartData(currentPosition);
        }, 30000);

    });
</script>

<!-- Add custom CSS for simple styling -->
<style>
    .quick-stat {
        transition: all 0.3s ease;
    }

    .quick-stat:hover {
        transform: scale(1.05);
    }

    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Filter form styling */
    .form-label.small {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    .form-control-sm, .form-select-sm {
        font-size: 0.875rem;
    }

    /* Loading state */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }

    .loading-overlay.show {
        display: flex;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced filtering functionality
    const filtersForm = document.getElementById('filtersForm');
    const districtFilter = document.getElementById('districtFilter');
    const countyFilter = document.getElementById('countyFilter');

    // Auto-submit form when filters change (with debouncing)
    let filterTimeout;

    function debounceFilter(callback, delay = 500) {
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(callback, delay);
    }

    // Auto-submit on filter change
    if (filtersForm) {
        const filterInputs = filtersForm.querySelectorAll('select, input[name="search"]');

        filterInputs.forEach(input => {
            if (input.name === 'search') {
                // Debounce search input
                input.addEventListener('input', function() {
                    debounceFilter(() => {
                        showLoadingOverlay();
                        filtersForm.submit();
                    });
                });
            } else {
                // Immediate submit for select filters
                input.addEventListener('change', function() {
                    showLoadingOverlay();
                    filtersForm.submit();
                });
            }
        });
    }

    // Dynamic county filtering based on district
    if (districtFilter && countyFilter) {
        const originalCountyOptions = Array.from(countyFilter.options);

        districtFilter.addEventListener('change', function() {
            const selectedDistrict = this.value;

            // Clear county options
            countyFilter.innerHTML = '<option value="">All Counties</option>';

            if (selectedDistrict) {
                // Filter counties based on district (you can enhance this with actual data)
                const filteredCounties = originalCountyOptions.filter(option => {
                    return option.value === '' || option.textContent.toLowerCase().includes(selectedDistrict.toLowerCase());
                });

                filteredCounties.forEach(option => {
                    if (option.value !== '') {
                        countyFilter.appendChild(option.cloneNode(true));
                    }
                });
            } else {
                // Restore all counties
                originalCountyOptions.forEach(option => {
                    if (option.value !== '') {
                        countyFilter.appendChild(option.cloneNode(true));
                    }
                });
            }
        });
    }

    // Loading overlay functions
    function showLoadingOverlay() {
        let overlay = document.querySelector('.loading-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">Applying filters...</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.classList.add('show');
    }

    function hideLoadingOverlay() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    // Hide loading overlay when page loads
    hideLoadingOverlay();

    // Performance optimization: Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Optimize table rendering for large datasets
    const table = document.getElementById('pollingStationsTable');
    if (table) {
        // Add virtual scrolling for very large tables (if needed)
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');

        // Only show visible rows to improve performance
        if (rows.length > 100) {
            console.log('Large dataset detected, optimizing rendering...');
            // You can implement virtual scrolling here if needed
        }
    }

    // Show live activity bar after page loads
    setTimeout(() => {
        const liveBar = document.getElementById('live-activity-bar');
        if (liveBar) {
            liveBar.classList.remove('d-none');

            // Auto-hide after 10 seconds
            setTimeout(() => {
                liveBar.classList.add('d-none');
            }, 10000);
        }
    }, 2000);

    // Enhanced vote update handling
    if (window.smoothRefresh) {
        // Override the original showVoteUpdateToast to add sound notification
        const originalShowToast = window.smoothRefresh.showVoteUpdateToast;
        window.smoothRefresh.showVoteUpdateToast = function(update) {
            // Call original method
            originalShowToast.call(this, update);

            // Add subtle sound notification (optional)
            try {
                // Create a subtle notification sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // Ignore audio errors
            }
        };
    }
});
</script>

<!-- Additional CSS for enhanced animations -->
<style>
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.vote-update-toast {
    animation: slideInRight 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#live-activity-bar {
    animation: slideInRight 0.5s ease-out;
}

/* Pulse animation for live indicator */
.spinner-border {
    animation: spinner-border 0.75s linear infinite, pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    }
}
</style>

@endpush
