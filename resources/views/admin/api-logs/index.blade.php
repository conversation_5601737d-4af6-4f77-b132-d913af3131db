@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card audit-header-card">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="header-content">
                            <h4 class="mb-1 fw-bold text-dark">
                                <i class="bi bi-journal-text me-2 text-gold"></i>
                                API Logs Monitoring
                            </h4>
                            <p class="text-muted mb-0 small">Monitor API requests and responses for Evidence and Vote endpoints</p>
                        </div>
                        <div class="d-flex gap-1 flex-wrap">
                            <a href="{{ route('admin.api-logs.export', request()->query()) }}" class="btn btn-compact btn-outline-gold">
                                <i class="bi bi-download"></i>
                                <span class="d-none d-md-inline ms-1">Export</span>
                            </a>
                            <button class="btn btn-compact btn-outline-gold" onclick="refreshStats()">
                                <i class="bi bi-arrow-clockwise"></i>
                                <span class="d-none d-lg-inline ms-1">Refresh</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card audit-stat-card audit-info hover-lift">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-info bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-graph-up text-info fs-5"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted small">Total Requests</h6>
                            <h4 class="mb-0 fw-bold text-dark">{{ number_format($stats['total_requests']) }}</h4>
                            @if($logs->hasPages())
                                <small class="text-muted">Showing {{ $logs->count() }} on this page</small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card audit-stat-card audit-success hover-lift">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-success bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-check-circle text-success fs-5"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted small">Success Rate</h6>
                            <h4 class="mb-0 fw-bold text-dark">{{ $stats['success_rate'] }}%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card audit-stat-card audit-warning hover-lift">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-clock text-warning fs-5"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted small">Avg Response Time</h6>
                            <h4 class="mb-0 fw-bold text-dark">{{ round($stats['average_response_time'] ?? 0) }}ms</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card audit-stat-card audit-danger hover-lift">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-flag text-danger fs-5"></i>
                        </div>
                        <div>
                            <h6 class="mb-0 text-muted small">Flagged Requests</h6>
                            <h4 class="mb-0 fw-bold text-dark">{{ number_format($stats['flagged_requests']) }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-activity me-2"></i>Recent Activity
                    </h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Last 5 minutes:</span>
                        <span class="fw-bold">{{ $stats['recent_activity']['last_5_minutes'] }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Last hour:</span>
                        <span class="fw-bold">{{ $stats['recent_activity']['last_hour'] }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted small">Active users (1h):</span>
                        <span class="fw-bold">{{ $stats['recent_activity']['active_users_last_hour'] }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-pie-chart me-2"></i>Request Types
                    </h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Evidence API:</span>
                        <span class="fw-bold text-primary">{{ number_format($stats['evidence_requests']) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Vote API:</span>
                        <span class="fw-bold text-success">{{ number_format($stats['vote_requests']) }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted small">Error Requests:</span>
                        <span class="fw-bold text-danger">{{ number_format($stats['error_requests']) }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-calendar-day me-2"></i>Daily Stats
                    </h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Today:</span>
                        <span class="fw-bold">{{ number_format($stats['today_requests']) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Yesterday:</span>
                        <span class="fw-bold">{{ number_format($stats['yesterday_requests']) }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted small">This week:</span>
                        <span class="fw-bold">{{ number_format($stats['week_requests']) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-3">
                    <form method="GET" action="{{ route('admin.api-logs.index') }}" class="row g-2">
                        <div class="col-md-2">
                            <select name="log_type" class="form-select form-select-sm">
                                <option value="">All Types</option>
                                <option value="evidence" {{ request('log_type') === 'evidence' ? 'selected' : '' }}>Evidence</option>
                                <option value="vote" {{ request('log_type') === 'vote' ? 'selected' : '' }}>Vote</option>
                                <option value="other" {{ request('log_type') === 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="status" class="form-select form-select-sm">
                                <option value="">All Status</option>
                                <option value="success" {{ request('status') === 'success' ? 'selected' : '' }}>Success</option>
                                <option value="error" {{ request('status') === 'error' ? 'selected' : '' }}>Error</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <select name="user_id" class="form-select form-select-sm">
                                <option value="">All Users</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->user_type }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <input type="date" name="date_from" class="form-control form-control-sm" 
                                   value="{{ request('date_from') }}" placeholder="From Date">
                        </div>
                        
                        <div class="col-md-2">
                            <input type="date" name="date_to" class="form-control form-control-sm" 
                                   value="{{ request('date_to') }}" placeholder="To Date">
                        </div>
                        
                        <div class="col-md-1">
                            <select name="per_page" class="form-select form-select-sm">
                                <option value="10" {{ request('per_page') == '10' ? 'selected' : '' }}>10</option>
                                <option value="25" {{ request('per_page', '25') == '25' ? 'selected' : '' }}>25</option>
                                <option value="50" {{ request('per_page') == '50' ? 'selected' : '' }}>50</option>
                                <option value="100" {{ request('per_page') == '100' ? 'selected' : '' }}>100</option>
                            </select>
                        </div>

                        <div class="col-md-1">
                            <div class="d-flex gap-1">
                                <button type="submit" class="btn btn-sm btn-primary">
                                    <i class="bi bi-funnel"></i> Filter
                                </button>
                                <a href="{{ route('admin.api-logs.index') }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-x-circle"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- API Logs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>API Request Logs
                        <span class="badge bg-secondary ms-2">{{ number_format($logs->total()) }} total</span>
                        @if($logs->hasPages())
                            <span class="badge bg-info ms-1">
                                Page {{ $logs->currentPage() }} of {{ $logs->lastPage() }}
                            </span>
                        @endif
                    </h6>

                    <div class="d-flex align-items-center gap-2">
                        @if(request()->hasAny(['log_type', 'status', 'user_id', 'date_from', 'date_to', 'endpoint', 'flagged']))
                            <span class="badge bg-warning">
                                <i class="bi bi-funnel"></i> Filtered
                            </span>
                        @endif

                        <!-- Bulk Actions -->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                    id="bulkActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false" disabled>
                                <i class="bi bi-check2-square"></i> Bulk Actions
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="bulkActionsDropdown">
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('flag')">
                                    <i class="bi bi-flag text-warning"></i> Flag Selected
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="bulkAction('unflag')">
                                    <i class="bi bi-flag-fill text-success"></i> Unflag Selected
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                                    <i class="bi bi-trash"></i> Delete Selected
                                </a></li>
                            </ul>
                        </div>

                        <small class="text-muted">
                            {{ $logs->perPage() }} per page
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>Timestamp</th>
                                    <th>Endpoint</th>
                                    <th>Method</th>
                                    <th>User</th>
                                    <th>Status</th>
                                    <th>Response Time</th>
                                    <th>Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($logs as $log)
                                    <tr class="{{ $log->is_flagged ? 'table-warning' : '' }}">
                                        <td>
                                            <input type="checkbox" class="form-check-input log-checkbox"
                                                   value="{{ $log->id }}" name="log_ids[]">
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ $log->created_at->format('M d, H:i:s') }}
                                            </small>
                                        </td>
                                        <td>
                                            <code class="small">{{ $log->endpoint }}</code>
                                            @if($log->is_flagged)
                                                <i class="bi bi-flag-fill text-warning ms-1" title="Flagged: {{ $log->flag_reason }}"></i>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $log->method === 'GET' ? 'info' : ($log->method === 'POST' ? 'success' : 'warning') }}">
                                                {{ $log->method }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($log->user)
                                                <div class="small">
                                                    <strong>{{ $log->user->name }}</strong><br>
                                                    <span class="text-muted">{{ $log->user->user_type }}</span>
                                                </div>
                                            @else
                                                <span class="text-muted">Anonymous</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $log->status_badge_color }}">
                                                {{ $log->response_status }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>{{ $log->formatted_response_time }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $log->log_type === 'evidence' ? 'primary' : ($log->log_type === 'vote' ? 'success' : 'secondary') }}">
                                                {{ ucfirst($log->log_type) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('admin.api-logs.show', $log) }}" 
                                                   class="btn btn-outline-primary btn-sm" title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @if(!$log->is_flagged)
                                                    <button class="btn btn-outline-warning btn-sm" 
                                                            onclick="flagLog({{ $log->id }})" title="Flag">
                                                        <i class="bi bi-flag"></i>
                                                    </button>
                                                @else
                                                    <form method="POST" action="{{ route('admin.api-logs.unflag', $log) }}" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-outline-success btn-sm" title="Unflag">
                                                            <i class="bi bi-flag-fill"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4 text-muted">
                                            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                            No API logs found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                
                @if($logs->hasPages())
                    <div class="card-footer">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center gap-3">
                                    <span class="text-muted small">
                                        Showing {{ $logs->firstItem() }} to {{ $logs->lastItem() }} of {{ number_format($logs->total()) }} results
                                    </span>

                                    @if($logs->hasPages())
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="text-muted small">Page:</span>
                                            <div class="btn-group btn-group-sm">
                                                @if($logs->onFirstPage())
                                                    <button class="btn btn-outline-secondary" disabled>
                                                        <i class="bi bi-chevron-left"></i>
                                                    </button>
                                                @else
                                                    <a href="{{ $logs->previousPageUrl() }}" class="btn btn-outline-secondary">
                                                        <i class="bi bi-chevron-left"></i>
                                                    </a>
                                                @endif

                                                <span class="btn btn-outline-secondary">
                                                    {{ $logs->currentPage() }} of {{ $logs->lastPage() }}
                                                </span>

                                                @if($logs->hasMorePages())
                                                    <a href="{{ $logs->nextPageUrl() }}" class="btn btn-outline-secondary">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </a>
                                                @else
                                                    <button class="btn btn-outline-secondary" disabled>
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="d-flex justify-content-end align-items-center gap-3">
                                    @if($logs->lastPage() > 1)
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="text-muted small">Go to page:</span>
                                            <form method="GET" action="{{ route('admin.api-logs.index') }}" class="d-flex align-items-center gap-1">
                                                @foreach(request()->except('page') as $key => $value)
                                                    <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                                                @endforeach
                                                <input type="number" name="page" class="form-control form-control-sm"
                                                       style="width: 70px;" min="1" max="{{ $logs->lastPage() }}"
                                                       value="{{ $logs->currentPage() }}" placeholder="Page">
                                                <button type="submit" class="btn btn-sm btn-outline-primary">Go</button>
                                            </form>
                                        </div>
                                    @endif

                                    {{ $logs->links('pagination::bootstrap-4') }}
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="card-footer">
                        <span class="text-muted small">
                            Showing {{ $logs->count() }} of {{ number_format($logs->total()) }} results
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Flag Modal -->
<div class="modal fade" id="flagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Flag API Log</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="flagForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for flagging:</label>
                        <select name="reason" id="reason" class="form-select" required>
                            <option value="">Select reason...</option>
                            <option value="suspicious_activity">Suspicious Activity</option>
                            <option value="error_pattern">Error Pattern</option>
                            <option value="performance_issue">Performance Issue</option>
                            <option value="security_concern">Security Concern</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Flag Log</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionTitle">Bulk Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkActionForm" method="POST" action="{{ route('admin.api-logs.bulk-action') }}">
                @csrf
                <div class="modal-body">
                    <div id="bulkActionContent">
                        <p>Are you sure you want to perform this action on the selected logs?</p>
                        <div class="mb-3" id="bulkReasonField" style="display: none;">
                            <label for="bulkReason" class="form-label">Reason for flagging:</label>
                            <select name="reason" id="bulkReason" class="form-select">
                                <option value="">Select reason...</option>
                                <option value="suspicious_activity">Suspicious Activity</option>
                                <option value="error_pattern">Error Pattern</option>
                                <option value="performance_issue">Performance Issue</option>
                                <option value="security_concern">Security Concern</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="alert alert-info">
                            <strong>Selected logs:</strong> <span id="selectedCount">0</span>
                        </div>
                    </div>
                    <input type="hidden" name="action" id="bulkActionType">
                    <div id="selectedLogIds"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="bulkActionSubmit">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function flagLog(logId) {
    const form = document.getElementById('flagForm');
    form.action = `/admin/api-logs/${logId}/flag`;
    new bootstrap.Modal(document.getElementById('flagModal')).show();
}

function refreshStats() {
    location.reload();
}

// Auto-submit form when per_page changes
document.addEventListener('DOMContentLoaded', function() {
    const perPageSelect = document.querySelector('select[name="per_page"]');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    // Add loading state to filter form
    const filterForm = document.querySelector('form[action*="api-logs"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Loading...';
                submitBtn.disabled = true;
            }
        });
    }
});

// Add keyboard shortcut for refresh (Ctrl+R or F5)
document.addEventListener('keydown', function(e) {
    if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
        e.preventDefault();
        refreshStats();
    }
});
</script>
@endsection
