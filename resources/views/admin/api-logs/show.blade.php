@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card audit-header-card">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="header-content">
                            <h4 class="mb-1 fw-bold text-dark">
                                <i class="bi bi-journal-text me-2 text-gold"></i>
                                API Log Details
                            </h4>
                            <p class="text-muted mb-0 small">
                                {{ $apiLog->endpoint_display_name }} - {{ $apiLog->created_at->format('M d, Y H:i:s') }}
                            </p>
                        </div>
                        <div class="d-flex gap-1 flex-wrap">
                            <a href="{{ route('admin.api-logs.index') }}" class="btn btn-compact btn-outline-gold">
                                <i class="bi bi-arrow-left"></i>
                                <span class="d-none d-md-inline ms-1">Back to Logs</span>
                            </a>
                            @if(!$apiLog->is_flagged)
                                <button class="btn btn-compact btn-outline-warning" onclick="flagLog({{ $apiLog->id }})">
                                    <i class="bi bi-flag"></i>
                                    <span class="d-none d-md-inline ms-1">Flag</span>
                                </button>
                            @else
                                <form method="POST" action="{{ route('admin.api-logs.unflag', $apiLog) }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-compact btn-outline-success">
                                        <i class="bi bi-flag-fill"></i>
                                        <span class="d-none d-md-inline ms-1">Unflag</span>
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status and Basic Info -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-info-circle me-2"></i>Request Status
                    </h6>
                    <div class="text-center">
                        <span class="badge bg-{{ $apiLog->status_badge_color }} fs-6 px-3 py-2">
                            {{ $apiLog->response_status }}
                        </span>
                        <div class="mt-2">
                            @if($apiLog->is_successful)
                                <i class="bi bi-check-circle text-success fs-4"></i>
                                <div class="small text-success mt-1">Successful</div>
                            @else
                                <i class="bi bi-x-circle text-danger fs-4"></i>
                                <div class="small text-danger mt-1">Failed</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-clock me-2"></i>Performance
                    </h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Response Time:</span>
                        <span class="fw-bold">{{ $apiLog->formatted_response_time }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted small">Request Size:</span>
                        <span class="fw-bold">{{ number_format($apiLog->metadata['request_size'] ?? 0) }} bytes</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted small">Response Size:</span>
                        <span class="fw-bold">{{ number_format($apiLog->metadata['response_size'] ?? 0) }} bytes</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-person me-2"></i>User Info
                    </h6>
                    @if($apiLog->user)
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted small">Name:</span>
                            <span class="fw-bold">{{ $apiLog->user->name }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted small">Type:</span>
                            <span class="badge bg-info">{{ $apiLog->user->user_type }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted small">Auth:</span>
                            <span class="fw-bold">{{ $apiLog->auth_method }}</span>
                        </div>
                    @else
                        <div class="text-center text-muted">
                            <i class="bi bi-person-x fs-4"></i>
                            <div class="small mt-1">Anonymous Request</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card {{ $apiLog->is_flagged ? 'border-warning' : '' }}">
                <div class="card-body p-3">
                    <h6 class="card-title mb-3">
                        <i class="bi bi-flag me-2"></i>Flag Status
                    </h6>
                    @if($apiLog->is_flagged)
                        <div class="text-center">
                            <i class="bi bi-flag-fill text-warning fs-4"></i>
                            <div class="small text-warning mt-1">{{ ucfirst(str_replace('_', ' ', $apiLog->flag_reason)) }}</div>
                            @if($apiLog->flaggedBy)
                                <div class="small text-muted mt-1">by {{ $apiLog->flaggedBy->name }}</div>
                            @endif
                        </div>
                    @else
                        <div class="text-center text-muted">
                            <i class="bi bi-flag fs-4"></i>
                            <div class="small mt-1">Not Flagged</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Request Details -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-arrow-up-circle me-2"></i>Request Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Endpoint:</strong>
                        <code class="ms-2">{{ $apiLog->method }} {{ $apiLog->endpoint }}</code>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Full URL:</strong>
                        <div class="small text-break">{{ $apiLog->url }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <strong>IP Address:</strong>
                        <code>{{ $apiLog->ip_address }}</code>
                    </div>
                    
                    @if($apiLog->location_data)
                        <div class="mb-3">
                            <strong>Location:</strong>
                            <div class="small">
                                Lat: {{ $apiLog->location_data['latitude'] }}, 
                                Lng: {{ $apiLog->location_data['longitude'] }}
                            </div>
                        </div>
                    @endif
                    
                    @if($apiLog->query_parameters)
                        <div class="mb-3">
                            <strong>Query Parameters:</strong>
                            <pre class="bg-light p-2 rounded small"><code>{{ json_encode($apiLog->query_parameters, JSON_PRETTY_PRINT) }}</code></pre>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-arrow-down-circle me-2"></i>Response Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Status Code:</strong>
                        <span class="badge bg-{{ $apiLog->status_badge_color }} ms-2">{{ $apiLog->response_status }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Response Time:</strong>
                        <span class="ms-2">{{ $apiLog->formatted_response_time }}</span>
                    </div>
                    
                    @if($apiLog->error_message)
                        <div class="mb-3">
                            <strong>Error Message:</strong>
                            <div class="alert alert-danger small mt-2">{{ $apiLog->error_message }}</div>
                        </div>
                    @endif
                    
                    @if($apiLog->response_headers)
                        <div class="mb-3">
                            <strong>Response Headers:</strong>
                            <div class="accordion accordion-flush" id="responseHeaders">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeaders">
                                            View Headers
                                        </button>
                                    </h2>
                                    <div id="collapseHeaders" class="accordion-collapse collapse" data-bs-parent="#responseHeaders">
                                        <div class="accordion-body">
                                            <pre class="bg-light p-2 rounded small"><code>{{ json_encode($apiLog->response_headers, JSON_PRETTY_PRINT) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Request Payload -->
    @if($apiLog->request_payload)
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-code-square me-2"></i>Request Payload
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>{{ $apiLog->request_payload }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Response Body -->
    @if($apiLog->response_body)
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-file-text me-2"></i>Response Body
                        </h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><code>{{ $apiLog->response_body }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- User Agent and Session Info -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-info-square me-2"></i>Additional Information
                    </h6>
                </div>
                <div class="card-body">
                    @if($apiLog->user_agent)
                        <div class="mb-3">
                            <strong>User Agent:</strong>
                            <div class="small text-break">{{ $apiLog->user_agent }}</div>
                        </div>
                    @endif
                    
                    @if($apiLog->session_id)
                        <div class="mb-3">
                            <strong>Session ID:</strong>
                            <code>{{ $apiLog->session_id }}</code>
                        </div>
                    @endif
                    
                    @if($apiLog->api_token_id)
                        <div class="mb-3">
                            <strong>API Token ID:</strong>
                            <code>{{ $apiLog->api_token_id }}</code>
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <strong>Log Type:</strong>
                        <span class="badge bg-{{ $apiLog->log_type === 'evidence' ? 'primary' : ($apiLog->log_type === 'vote' ? 'success' : 'secondary') }}">
                            {{ ucfirst($apiLog->log_type) }}
                        </span>
                    </div>
                    
                    <div class="mb-0">
                        <strong>Timestamp:</strong>
                        <span class="ms-2">{{ $apiLog->created_at->format('M d, Y H:i:s T') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Flag Modal -->
<div class="modal fade" id="flagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Flag API Log</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="flagForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for flagging:</label>
                        <select name="reason" id="reason" class="form-select" required>
                            <option value="">Select reason...</option>
                            <option value="suspicious_activity">Suspicious Activity</option>
                            <option value="error_pattern">Error Pattern</option>
                            <option value="performance_issue">Performance Issue</option>
                            <option value="security_concern">Security Concern</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Flag Log</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function flagLog(logId) {
    const form = document.getElementById('flagForm');
    form.action = `/admin/api-logs/${logId}/flag`;
    new bootstrap.Modal(document.getElementById('flagModal')).show();
}
</script>
@endsection
