@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <!-- User Profile Header -->
            <div class="user-profile-header mb-4">
                <div class="card card-custom">
                    <div class="card-header-gold">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="profile-avatar me-4">
                                    <div class="avatar-large">
                                        <span class="avatar-text-large">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                                    </div>
                                </div>
                                <div class="profile-info">
                                    <h3 class="mb-1 text-white fw-bold">{{ $user->name }}</h3>
                                    <div class="d-flex align-items-center gap-3">
                                        <span class="badge badge-user-type">
                                            <i class="bi bi-person-badge me-1"></i>
                                            {{ ucfirst($user->user_type) }}
                                        </span>
                                        @if($user->is_active)
                                            <span class="badge badge-status-active">
                                                <i class="bi bi-check-circle me-1"></i>
                                                Active
                                            </span>
                                        @else
                                            <span class="badge badge-status-inactive">
                                                <i class="bi bi-x-circle me-1"></i>
                                                Inactive
                                            </span>
                                        @endif
                                    </div>
                                    <small class="text-white-50 mt-1">
                                        <i class="bi bi-telephone me-1"></i>
                                        {{ $user->phone_number }}
                                    </small>
                                </div>
                            </div>
                            <div class="profile-actions">
                                @if(auth()->user()->hasPermission('edit_users'))
                                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-gold-outline btn-modern me-2">
                                    <i class="bi bi-pencil me-2"></i>
                                    Edit User
                                </a>
                                @endif
                                <a href="{{ route('admin.users.index') }}" class="btn btn-light btn-modern">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    Back to Users
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Details Content -->
            <div class="row g-4">
                <!-- User Information Card -->
                <div class="col-lg-6">
                    <div class="card card-custom h-100">
                        <div class="card-header-light">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bi bi-person-fill me-2 text-primary"></i>
                                User Information
                            </h6>
                        </div>
                        <div class="card-body-compact">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-person me-2"></i>
                                        Full Name
                                    </div>
                                    <div class="info-value">{{ $user->name }}</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-telephone me-2"></i>
                                        Phone Number
                                    </div>
                                    <div class="info-value">
                                        <a href="tel:{{ $user->phone_number }}" class="text-decoration-none">
                                            {{ $user->phone_number }}
                                        </a>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-person-badge me-2"></i>
                                        User Type
                                    </div>
                                    <div class="info-value">
                                        <span class="badge badge-user-type-detail">
                                            {{ ucfirst($user->user_type) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-toggle-on me-2"></i>
                                        Account Status
                                    </div>
                                    <div class="info-value">
                                        @if($user->is_active)
                                            <span class="status-badge status-active">
                                                <i class="bi bi-check-circle me-1"></i>
                                                Active
                                            </span>
                                        @else
                                            <span class="status-badge status-inactive">
                                                <i class="bi bi-x-circle me-1"></i>
                                                Inactive
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-calendar-plus me-2"></i>
                                        Created Date
                                    </div>
                                    <div class="info-value">
                                        <div>{{ $user->created_at->format('M d, Y') }}</div>
                                        <small class="text-muted">{{ $user->created_at->format('H:i') }}</small>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-clock-history me-2"></i>
                                        Last Login
                                    </div>
                                    <div class="info-value">
                                        @if($user->last_login_at)
                                            <div>{{ $user->last_login_at->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $user->last_login_at->format('H:i') }}</small>
                                        @else
                                            <span class="text-muted">
                                                <i class="bi bi-dash-circle me-1"></i>
                                                Never logged in
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role and Permissions Card -->
                <div class="col-lg-6">
                    <div class="card card-custom h-100">
                        <div class="card-header-light">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bi bi-shield-check me-2 text-warning"></i>
                                Role & Permissions
                            </h6>
                        </div>
                        <div class="card-body-compact">
                            @if($user->role)
                                <div class="role-section mb-4">
                                    <div class="role-header">
                                        <h6 class="role-title">Primary Role</h6>
                                        <span class="badge badge-role-primary">
                                            <i class="bi bi-shield-fill me-1"></i>
                                            {{ $user->role->display_name }}
                                        </span>
                                    </div>
                                    @if($user->role->description)
                                    <p class="role-description">{{ $user->role->description }}</p>
                                    @endif
                                </div>

                                <div class="permissions-section">
                                    <h6 class="permissions-title">
                                        <i class="bi bi-key me-2"></i>
                                        Permissions
                                    </h6>
                                    @if($user->role->permissions && count($user->role->permissions) > 0)
                                        <div class="permissions-grid">
                                            @foreach($user->role->permissions as $permission)
                                                <div class="permission-item">
                                                    <span class="badge badge-permission">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                    </span>
                                                </div>
                                            @endforeach
                                        </div>
                                        <div class="no-permissions">
                                            <i class="bi bi-info-circle me-2"></i>
                                            <span class="text-muted">No permissions assigned to this role</span>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="alert alert-warning-custom">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>No Role Assigned</strong>
                                    <p class="mb-0">This user doesn't have a primary role assigned. Consider assigning a role to grant appropriate permissions.</p>
                                </div>
                            @endif

                            @if($user->roles->count() > 1)
                                <div class="additional-roles-section mt-4">
                                    <h6 class="additional-roles-title">
                                        <i class="bi bi-layers me-2"></i>
                                        Additional Roles
                                    </h6>
                                    <div class="additional-roles-grid">
                                        @foreach($user->roles as $role)
                                            @if($role->id !== $user->role_id)
                                                <span class="badge badge-additional-role">
                                                    <i class="bi bi-plus-circle me-1"></i>
                                                    {{ $role->display_name }}
                                                </span>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            @if(auth()->check() && auth()->user()->hasPermission('edit_users') && $user->id !== auth()->id())
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card card-custom">
                        <div class="card-header-light">
                            <h6 class="mb-0 fw-semibold">
                                <i class="bi bi-gear me-2 text-secondary"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body-compact">
                            <div class="actions-grid">
                                <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="action-form">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="btn btn-action-modern btn-{{ $user->is_active ? 'warning' : 'success' }}"
                                            onclick="return confirm('Are you sure you want to {{ $user->is_active ? 'deactivate' : 'activate' }} this user?')">
                                        <i class="bi bi-{{ $user->is_active ? 'pause-circle' : 'play-circle' }} me-2"></i>
                                        {{ $user->is_active ? 'Deactivate' : 'Activate' }} User
                                    </button>
                                </form>

                                @if(auth()->user()->hasPermission('delete_users'))
                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="action-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-action-modern btn-danger"
                                            onclick="return confirm('⚠️ Are you sure you want to delete this user?\n\nThis action cannot be undone and will permanently remove all user data.')">
                                        <i class="bi bi-trash me-2"></i>
                                        Delete User
                                    </button>
                                </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<style>
/* ===== USER PROFILE HEADER ===== */
.user-profile-header .profile-avatar .avatar-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.avatar-text-large {
    color: white;
    font-weight: 700;
    font-size: 2rem;
}

.badge-user-type {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

.badge-status-active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

.badge-status-inactive {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* ===== INFO GRID ===== */
.info-grid {
    display: grid;
    gap: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #FFA500;
    transition: all 0.2s ease;
}

.info-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    min-width: 140px;
}

.info-value {
    text-align: right;
    color: #2c3e50;
    font-weight: 500;
}

.badge-user-type-detail {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

/* ===== ROLE SECTION ===== */
.role-section {
    padding: 1.5rem;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 12px;
    border: 1px solid #ffeaa7;
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.role-title {
    color: #856404;
    font-weight: 600;
    margin: 0;
}

.badge-role-primary {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.role-description {
    color: #856404;
    font-style: italic;
    margin: 0;
    font-size: 0.9rem;
}

/* ===== PERMISSIONS SECTION ===== */
.permissions-section {
    margin-top: 1.5rem;
}

.permissions-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
}

.permission-item {
    display: flex;
}

.badge-permission {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    width: 100%;
    text-align: center;
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.no-permissions {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    color: #6c757d;
}

.alert-warning-custom {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    padding: 1.5rem;
    color: #856404;
}

.alert-warning-custom strong {
    color: #856404;
}

/* ===== ADDITIONAL ROLES ===== */
.additional-roles-section {
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.additional-roles-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.additional-roles-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.badge-additional-role {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* ===== ACTIONS SECTION ===== */
.actions-grid {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-form {
    flex: 1;
    min-width: 200px;
}

.btn-action-modern {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-action-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .user-profile-header .profile-avatar .avatar-large {
        width: 60px;
        height: 60px;
    }

    .avatar-text-large {
        font-size: 1.5rem;
    }

    .profile-actions {
        margin-top: 1rem;
        width: 100%;
    }

    .profile-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .info-label {
        min-width: auto;
    }

    .info-value {
        text-align: left;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        flex-direction: column;
    }

    .action-form {
        min-width: auto;
    }
}
</style>
@endsection
