@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header-gold d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <div class="header-icon-wrapper me-3">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <div>
                            <h4 class="mb-0 text-white fw-bold">User Management</h4>
                            <small class="text-white-50">Manage system users and permissions</small>
                        </div>
                    </div>
                    @if(auth()->user()->hasPermission('create_users'))
                    <a href="{{ route('admin.users.create') }}" class="btn btn-gold-outline btn-modern">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add New User
                    </a>
                    @endif
                </div>

                <div class="card-body-compact">
                    <!-- Enhanced Filters Section -->
                    <div class="filters-section mb-4">
                        <form method="GET" class="filter-form">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label for="search" class="form-label-custom">
                                        <i class="bi bi-search me-1"></i>Search Users
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="bi bi-search text-muted"></i>
                                        </span>
                                        <input type="text" class="form-control-custom border-start-0" id="search" name="search"
                                               value="{{ request('search') }}" placeholder="Search by name or phone">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label for="role" class="form-label-custom">
                                        <i class="bi bi-shield-check me-1"></i>Filter by Role
                                    </label>
                                    <select class="form-control-custom" id="role" name="role">
                                        <option value="">All Roles</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}" {{ request('role') == $role->id ? 'selected' : '' }}>
                                                {{ $role->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="status" class="form-label-custom">
                                        <i class="bi bi-toggle-on me-1"></i>Status
                                    </label>
                                    <select class="form-control-custom" id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="per_page" class="form-label-custom">
                                        <i class="bi bi-list-ol me-1"></i>Per Page
                                    </label>
                                    <select class="form-control-custom" id="per_page" name="per_page" onchange="this.form.submit()">
                                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-gold btn-modern w-100">
                                        <i class="bi bi-funnel"></i>
                                    </button>
                                </div>
                            </div>

                            @if(request()->hasAny(['search', 'role', 'status']))
                            <div class="mt-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="active-filters">
                                        <span class="text-muted me-2">Active filters:</span>
                                        @if(request('search'))
                                            <span class="badge bg-primary me-1">Search: {{ request('search') }}</span>
                                        @endif
                                        @if(request('role'))
                                            <span class="badge bg-info me-1">Role: {{ $roles->find(request('role'))->display_name ?? 'Unknown' }}</span>
                                        @endif
                                        @if(request('status'))
                                            <span class="badge bg-success me-1">Status: {{ ucfirst(request('status')) }}</span>
                                        @endif
                                    </div>
                                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-x-circle me-1"></i>
                                        Clear All
                                    </a>
                                </div>
                            </div>
                            @endif
                        </form>
                    </div>

                    <!-- Enhanced Users Table -->
                    <div class="users-table-section">
                        <div class="table-header mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="table-info">
                                    <h6 class="mb-0 text-dark fw-semibold">
                                        <i class="bi bi-table me-2 text-primary"></i>
                                        Users List
                                    </h6>
                                    <small class="text-muted">{{ $users->total() }} total users found</small>
                                </div>
                                <div class="table-actions">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary active" data-view="table">
                                            <i class="bi bi-table"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" data-view="grid">
                                            <i class="bi bi-grid-3x3-gap"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions Toolbar -->
                        <div class="bulk-actions-toolbar mb-3" id="bulkActionsToolbar" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="selected-info">
                                    <span class="selected-count">0</span> users selected
                                </div>
                                <div class="bulk-actions">
                                    @if(auth()->user()->hasPermission('edit_users'))
                                    <button type="button" class="btn btn-sm btn-success me-2" onclick="bulkAction('activate')">
                                        <i class="bi bi-check-circle me-1"></i>
                                        Activate
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning me-2" onclick="bulkAction('deactivate')">
                                        <i class="bi bi-pause-circle me-1"></i>
                                        Deactivate
                                    </button>
                                    @endif
                                    @if(auth()->user()->hasPermission('delete_users'))
                                    <button type="button" class="btn btn-sm btn-danger me-2" onclick="bulkAction('delete')">
                                        <i class="bi bi-trash me-1"></i>
                                        Delete
                                    </button>
                                    @endif
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="clearSelection()">
                                        <i class="bi bi-x-circle me-1"></i>
                                        Clear Selection
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive table-modern">
                            <table class="table table-custom table-hover">
                                <thead class="table-header-modern">
                                    <tr>
                                        <th class="border-0" style="width: 50px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll">
                                                <label class="form-check-label" for="selectAll"></label>
                                            </div>
                                        </th>
                                        <th class="border-0">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-person me-2"></i>User
                                            </div>
                                        </th>
                                        <th class="border-0">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-telephone me-2"></i>Contact
                                            </div>
                                        </th>
                                        <th class="border-0">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-shield-check me-2"></i>Role
                                            </div>
                                        </th>
                                        <th class="border-0">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-toggle-on me-2"></i>Status
                                            </div>
                                        </th>
                                        <th class="border-0">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-clock me-2"></i>Last Login
                                            </div>
                                        </th>
                                        <th class="border-0 text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <i class="bi bi-gear me-2"></i>Actions
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($users as $user)
                                    <tr class="user-row">
                                        <td class="border-0">
                                            <div class="form-check">
                                                <input class="form-check-input user-checkbox" type="checkbox" value="{{ $user->id }}" id="user_{{ $user->id }}">
                                                <label class="form-check-label" for="user_{{ $user->id }}"></label>
                                            </div>
                                        </td>
                                        <td class="border-0">
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-3">
                                                    <div class="avatar-circle">
                                                        <span class="avatar-text">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                                                    </div>
                                                </div>
                                                <div class="user-info">
                                                    <div class="user-name">{{ $user->name }}</div>
                                                    <div class="user-type">{{ ucfirst($user->user_type) }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="border-0">
                                            <div class="contact-info">
                                                <div class="phone-number">{{ $user->phone_number }}</div>
                                                <small class="text-muted">Primary contact</small>
                                            </div>
                                        </td>
                                        <td class="border-0">
                                            @if($user->role)
                                                <span class="badge badge-role bg-info-subtle text-info">
                                                    <i class="bi bi-shield-check me-1"></i>
                                                    {{ $user->role->display_name }}
                                                </span>
                                            @else
                                                <span class="badge badge-role bg-secondary-subtle text-secondary">
                                                    <i class="bi bi-shield-x me-1"></i>
                                                    No Role
                                                </span>
                                            @endif
                                        </td>
                                        <td class="border-0">
                                            @if($user->is_active)
                                                <span class="status-badge status-active">
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    Active
                                                </span>
                                            @else
                                                <span class="status-badge status-inactive">
                                                    <i class="bi bi-x-circle me-1"></i>
                                                    Inactive
                                                </span>
                                            @endif
                                        </td>
                                        <td class="border-0">
                                            <div class="login-info">
                                                @if($user->last_login_at)
                                                    <div class="login-date">{{ $user->last_login_at->format('M d, Y') }}</div>
                                                    <small class="login-time text-muted">{{ $user->last_login_at->format('H:i') }}</small>
                                                @else
                                                    <span class="text-muted">
                                                        <i class="bi bi-dash-circle me-1"></i>
                                                        Never logged in
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="border-0">
                                            <div class="action-buttons d-flex justify-content-center">
                                                @if(auth()->user()->hasPermission('view_users'))
                                                <a href="{{ route('admin.users.show', $user) }}"
                                                   class="btn btn-action btn-view me-1"
                                                   data-bs-toggle="tooltip"
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                @endif

                                                @if(auth()->user()->hasPermission('edit_users'))
                                                <a href="{{ route('admin.users.edit', $user) }}"
                                                   class="btn btn-action btn-edit me-1"
                                                   data-bs-toggle="tooltip"
                                                   title="Edit User">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                @endif

                                                @if(auth()->user()->hasPermission('edit_users'))
                                                <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit"
                                                            class="btn btn-action btn-toggle me-1"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ $user->is_active ? 'Deactivate' : 'Activate' }} User"
                                                            onclick="return confirm('Are you sure you want to {{ $user->is_active ? 'deactivate' : 'activate' }} this user?')">
                                                        <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                                    </button>
                                                </form>
                                                @endif

                                                @if(auth()->user()->hasPermission('delete_users') && $user->id !== auth()->id())
                                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit"
                                                            class="btn btn-action btn-delete"
                                                            data-bs-toggle="tooltip"
                                                            title="Delete User"
                                                            onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="6" class="border-0">
                                            <div class="empty-state text-center py-5">
                                                <div class="empty-icon mb-3">
                                                    <i class="bi bi-people text-muted"></i>
                                                </div>
                                                <h6 class="text-muted mb-2">No Users Found</h6>
                                                <p class="text-muted mb-3">
                                                    @if(request()->hasAny(['search', 'role', 'status']))
                                                        No users match your current filters. Try adjusting your search criteria.
                                                    @else
                                                        There are no users in the system yet.
                                                    @endif
                                                </p>
                                                @if(auth()->user()->hasPermission('create_users'))
                                                <a href="{{ route('admin.users.create') }}" class="btn btn-gold btn-modern">
                                                    <i class="bi bi-plus-circle me-2"></i>
                                                    Add First User
                                                </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Enhanced Pagination -->
                    @if($users->hasPages())
                    <div class="pagination-section mt-4 pt-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="pagination-info-enhanced">
                                <div class="d-flex align-items-center">
                                    <div class="pagination-icon me-2">
                                        <i class="bi bi-info-circle"></i>
                                    </div>
                                    <div>
                                        <div class="pagination-text">
                                            Showing <strong>{{ $users->firstItem() }}</strong> to <strong>{{ $users->lastItem() }}</strong> of <strong>{{ $users->total() }}</strong> users
                                        </div>
                                        <small class="text-muted">Page {{ $users->currentPage() }} of {{ $users->lastPage() }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="pagination-controls">
                                {{ $users->links() }}
                            </div>
                        </div>
                    </div>
                    @elseif($users->total() > 0)
                    <div class="pagination-section mt-4 pt-4">
                        <div class="pagination-info-enhanced text-center">
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="pagination-icon me-2">
                                    <i class="bi bi-check-circle text-success"></i>
                                </div>
                                <div>
                                    <div class="pagination-text">
                                        Showing all <strong>{{ $users->total() }}</strong> users
                                    </div>
                                    <small class="text-muted">Complete list displayed</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* ===== HEADER ENHANCEMENTS ===== */
.header-icon-wrapper {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.btn-modern {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* ===== FILTERS SECTION ===== */
.filters-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.filter-form .input-group-text {
    background: transparent;
    border-color: #e9ecef;
}

.active-filters .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* ===== TABLE ENHANCEMENTS ===== */
.users-table-section {
    margin-top: 2rem;
}

.table-header {
    background: #fff;
    border-radius: 8px 8px 0 0;
    padding: 1rem 1.5rem;
    border: 1px solid #e9ecef;
    border-bottom: none;
}

.table-modern {
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
    border-top: none;
}

.table-header-modern th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    padding: 1rem 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-row {
    transition: all 0.2s ease;
}

.user-row:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* ===== USER AVATAR ===== */
.user-avatar .avatar-circle {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
}

.avatar-text {
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
}

.user-info .user-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.user-info .user-type {
    color: #6c757d;
    font-size: 0.8rem;
    text-transform: capitalize;
}

/* ===== CONTACT INFO ===== */
.contact-info .phone-number {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

/* ===== BADGES ===== */
.badge-role {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: 500;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ===== LOGIN INFO ===== */
.login-info .login-date {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.login-info .login-time {
    font-size: 0.75rem;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    gap: 0.5rem;
}

.btn-action {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-view {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-view:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    color: white;
}

.btn-edit:hover {
    background: linear-gradient(135deg, #FF8C00 0%, #FF7F00 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
    color: white;
}

.btn-toggle {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: white;
}

.btn-toggle:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    color: white;
}

/* ===== EMPTY STATE ===== */
.empty-state {
    padding: 3rem 2rem;
}

.empty-icon i {
    font-size: 4rem;
    opacity: 0.3;
}

/* ===== PAGINATION ENHANCEMENTS ===== */
.pagination-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.pagination-info-enhanced {
    color: #495057;
}

.pagination-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.pagination-text {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #adb5bd;
    color: #495057;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%);
    border-color: #FFA500;
    color: white;
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

/* ===== BULK ACTIONS TOOLBAR ===== */
.bulk-actions-toolbar {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #90caf9;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.selected-info {
    font-weight: 600;
    color: #1565c0;
    display: flex;
    align-items: center;
}

.selected-count {
    background: #1976d2;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    min-width: 24px;
    text-align: center;
}

.bulk-actions .btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.bulk-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Enhanced checkbox styling */
.form-check-input {
    width: 1.2em;
    height: 1.2em;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.form-check-input:checked {
    background-color: #FFA500;
    border-color: #FFA500;
    box-shadow: 0 0 0 0.2rem rgba(255, 165, 0, 0.25);
}

.form-check-input:focus {
    border-color: #FFA500;
    box-shadow: 0 0 0 0.2rem rgba(255, 165, 0, 0.25);
}

.form-check-input:indeterminate {
    background-color: #FFA500;
    border-color: #FFA500;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .filters-section {
        padding: 1rem;
    }

    .table-header {
        padding: 0.75rem 1rem;
    }

    .table-header-modern th {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }

    .user-avatar .avatar-circle {
        width: 35px;
        height: 35px;
    }

    .avatar-text {
        font-size: 0.9rem;
    }

    .btn-action {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .pagination-section {
        padding: 1rem;
    }

    .pagination-info-enhanced {
        margin-bottom: 1rem;
    }

    .bulk-actions-toolbar {
        padding: 0.75rem 1rem;
    }

    .bulk-actions {
        margin-top: 0.5rem;
        width: 100%;
    }

    .bulk-actions .btn {
        margin-bottom: 0.25rem;
        font-size: 0.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form on per_page change
    const perPageSelect = document.getElementById('per_page');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    // Enhanced search functionality
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 3 || this.value.length === 0) {
                    // Auto-submit after 500ms of no typing
                    // this.form.submit();
                }
            }, 500);
        });
    }

    // Table view toggle functionality
    const viewButtons = document.querySelectorAll('[data-view]');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const view = this.dataset.view;
            // Here you could implement grid view vs table view
            console.log('Switching to view:', view);
        });
    });

    // Enhanced row hover effects
    const userRows = document.querySelectorAll('.user-row');
    userRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Smooth scroll to top after pagination
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function() {
            setTimeout(() => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 100);
        });
    });

    // Enhanced confirmation dialogs
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const form = this.closest('form');

            // Create custom confirmation modal (you can replace with SweetAlert2 if available)
            if (confirm('⚠️ Are you sure you want to delete this user?\n\nThis action cannot be undone and will permanently remove all user data.')) {
                form.submit();
            }
        });
    });

    // Status toggle confirmations
    const toggleButtons = document.querySelectorAll('.btn-toggle');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const form = this.closest('form');
            const action = this.title.toLowerCase();

            if (confirm(`Are you sure you want to ${action.replace(' user', '')} this user?`)) {
                form.submit();
            }
        });
    });

    // Bulk actions functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActionsToolbar = document.getElementById('bulkActionsToolbar');
    const selectedCountSpan = document.querySelector('.selected-count');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsToolbar();
        });
    }

    // Individual checkbox functionality
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionsToolbar();

            // Update select all checkbox state
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === userCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < userCheckboxes.length;
        });
    });

    function updateBulkActionsToolbar() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const count = checkedBoxes.length;

        if (count > 0) {
            bulkActionsToolbar.style.display = 'block';
            selectedCountSpan.textContent = count;
        } else {
            bulkActionsToolbar.style.display = 'none';
        }
    }

    // Global functions for bulk actions
    window.bulkAction = function(action) {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const userIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (userIds.length === 0) {
            alert('Please select at least one user.');
            return;
        }

        let confirmMessage = '';
        switch(action) {
            case 'activate':
                confirmMessage = `Are you sure you want to activate ${userIds.length} user(s)?`;
                break;
            case 'deactivate':
                confirmMessage = `Are you sure you want to deactivate ${userIds.length} user(s)?`;
                break;
            case 'delete':
                confirmMessage = `⚠️ Are you sure you want to delete ${userIds.length} user(s)?\n\nThis action cannot be undone and will permanently remove all user data.`;
                break;
        }

        if (confirm(confirmMessage)) {
            // Create and submit form for bulk action
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("admin.users.bulk-action") }}';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add action
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = action;
            form.appendChild(actionInput);

            // Add user IDs
            userIds.forEach(id => {
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'user_ids[]';
                idInput.value = id;
                form.appendChild(idInput);
            });

            document.body.appendChild(form);
            form.submit();
        }
    };

    window.clearSelection = function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        updateBulkActionsToolbar();
    };
});
</script>
@endsection
