@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    


    <!-- Enhanced Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-primary">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($totalStations) }}</div>
                        <div class="stat-label">Total Stations</div>
                        <div class="stat-trend">
                            <i class="bi bi-graph-up"></i>
                            <span>All registered</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">100%</span>
                        <small class="percentage-label">Complete</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-success">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($submittedStations) }}</div>
                        <div class="stat-label">Submitted</div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i>
                            <span>{{ $totalStations > 0 ? number_format(($submittedStations / $totalStations) * 100, 1) : 0 }}% complete</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">{{ $totalStations > 0 ? number_format(($submittedStations / $totalStations) * 100, 1) : 0 }}%</span>
                        <small class="percentage-label">Submitted</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-warning">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($pendingStations) }}</div>
                        <div class="stat-label">Pending</div>
                        <div class="stat-trend">
                            <i class="bi bi-hourglass-split"></i>
                            <span>Awaiting submission</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">{{ $totalStations > 0 ? number_format(($pendingStations / $totalStations) * 100, 1) : 0 }}%</span>
                        <small class="percentage-label">Pending</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-info">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-bar-chart-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($totalVotes) }}</div>
                        <div class="stat-label">Total Votes</div>
                        <div class="stat-trend">
                            <i class="bi bi-people"></i>
                            <span>Across all stations</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text"><i class="bi bi-infinity"></i></span>
                        <small class="percentage-label">Total Votes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ultra Compact Smart Filters -->
    <div class="ultra-compact-filter-section mb-4">
        <form method="GET" class="ultra-compact-filter-form" id="filterForm">
            <div class="ultra-compact-filter-container">
                <!-- Filter Title with Quick Stats -->
                <div class="filter-header-compact">
                    <div class="filter-title-compact">
                        <i class="bi bi-funnel-fill"></i>
                        <span>Filters</span>
                        <span class="filter-count-badge">{{ $pollingStations->total() }}</span>
                    </div>
                    <div class="filter-quick-actions">
                        <button type="button" class="btn-quick-action" onclick="clearAllFilters()" title="Clear All">
                            <i class="bi bi-x-circle"></i>
                        </button>
                        <button type="button" class="btn-quick-action" onclick="toggleFilterExpand()" title="Expand/Collapse">
                            <i class="bi bi-chevron-down" id="expandIcon"></i>
                        </button>
                    </div>
                </div>

                <!-- Compact Filter Row -->
                <div class="filter-row-compact" id="filterRow">
                    <div class="filter-group">
                        <div class="filter-input-wrapper">
                            <i class="bi bi-search filter-icon"></i>
                            <input type="text" class="form-control-ultra-compact" id="search" name="search"
                                   value="{{ request('search') }}" placeholder="Search stations..." autocomplete="off">
                        </div>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="district" name="district" onchange="this.form.submit()">
                            <option value="">District</option>
                            @foreach($districts as $dist)
                                <option value="{{ $dist }}" {{ request('district') == $dist ? 'selected' : '' }}>
                                    {{ $dist }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="county" name="county" onchange="this.form.submit()">
                            <option value="">County</option>
                            @foreach($counties as $cnty)
                                <option value="{{ $cnty }}" {{ request('county') == $cnty ? 'selected' : '' }}>
                                    {{ $cnty }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="subcounty" name="subcounty" onchange="this.form.submit()">
                            <option value="">Subcounty</option>
                            @foreach($subcounties as $sub)
                                <option value="{{ $sub }}" {{ request('subcounty') == $sub ? 'selected' : '' }}>
                                    {{ $sub }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="parish" name="parish" onchange="this.form.submit()">
                            <option value="">Parish</option>
                            @foreach($parishes as $par)
                                <option value="{{ $par }}" {{ request('parish') == $par ? 'selected' : '' }}>
                                    {{ $par }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="village" name="village" onchange="this.form.submit()">
                            <option value="">Village</option>
                            @foreach($villages as $vil)
                                <option value="{{ $vil }}" {{ request('village') == $vil ? 'selected' : '' }}>
                                    {{ $vil }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="filter-group">
                        <select class="form-control-ultra-compact" id="status" name="status" onchange="this.form.submit()">
                            <option value="">Status</option>
                            <option value="submitted" {{ request('status') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>

                    <div class="filter-actions-ultra-compact">
                        <button type="submit" class="btn-filter-apply" title="Apply Filters">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Active Filters Display -->
                <div class="active-filters-display" id="activeFilters" style="display: none;">
                    <!-- Active filters will be populated by JavaScript -->
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Polling Stations Table -->
    <div class="data-table-section">
        <div class="data-table-header">
            <div class="data-table-title">
                <div class="title-icon">
                    <i class="bi bi-building-fill"></i>
                </div>
                <div class="title-content">
                    <h3>Polling Stations</h3>
                    <p>Manage vote submissions and evidence uploads</p>
                </div>
            </div>
            <div class="data-table-actions">
                <div class="table-stats">
                    <span class="stat-badge">
                        <i class="bi bi-building"></i>
                        {{ $pollingStations->total() }} stations
                    </span>
                    <span class="stat-badge stat-badge-success">
                        <i class="bi bi-check-circle"></i>
                        {{ $submittedStations }} submitted
                    </span>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-table-action" onclick="exportData()">
                        <i class="bi bi-download"></i>
                        Export
                    </button>
                    <button class="btn btn-table-action btn-primary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <div class="data-table-container">
            <div class="table-wrapper">
                <table class="data-table">
                    <thead class="table-header">
                        <tr>
                            <th class="th-station">
                                <div class="th-content">
                                    <i class="bi bi-building"></i>
                                    <span>Station</span>
                                </div>
                            </th>
                            <th class="th-location">
                                <div class="th-content">
                                    <i class="bi bi-geo-alt"></i>
                                    <span>Location</span>
                                </div>
                            </th>
                            <th class="th-agent">
                                <div class="th-content">
                                    <i class="bi bi-person"></i>
                                    <span>Agent</span>
                                </div>
                            </th>
                            <th class="th-status">
                                <div class="th-content">
                                    <i class="bi bi-check-circle"></i>
                                    <span>Status</span>
                                </div>
                            </th>
                            <th class="th-votes">
                                <div class="th-content">
                                    <i class="bi bi-bar-chart"></i>
                                    <span>Votes</span>
                                </div>
                            </th>
                            <th class="th-evidence">
                                <div class="th-content">
                                    <i class="bi bi-camera"></i>
                                    <span>Evidence</span>
                                </div>
                            </th>
                            <th class="th-actions">
                                <div class="th-content">
                                    <i class="bi bi-gear"></i>
                                    <span>Actions</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        @forelse($pollingStations as $station)
                        <tr class="table-row" data-station-id="{{ $station->id }}">
                            <td class="td-station">
                                <div class="station-cell">
                                    <div class="station-avatar">
                                        <i class="bi bi-building"></i>
                                    </div>
                                    <div class="station-info">
                                        <div class="station-name">{{ $station->name }}</div>
                                        <div class="station-id">ID: {{ $station->id }}</div>
                                    </div>
                                </div>
                            </td>

                            <td class="td-location">
                                <div class="location-cell">
                                    <div class="location-primary">{{ $station->district }}</div>
                                    <div class="location-secondary">
                                        <i class="bi bi-geo-alt"></i>
                                        {{ $station->county }}, {{ $station->subcounty }}
                                    </div>
                                    @if($station->parish || $station->village)
                                    <div class="location-tertiary">
                                        <i class="bi bi-house-door"></i>
                                        @if($station->parish && $station->village)
                                            {{ $station->parish }}, {{ $station->village }}
                                        @elseif($station->parish)
                                            {{ $station->parish }}
                                        @else
                                            {{ $station->village }}
                                        @endif
                                    </div>
                                    @endif
                                </div>
                            </td>

                            <td class="td-agent">
                                @if($station->agent && $station->agent->user)
                                    <div class="agent-cell">
                                        <div class="agent-avatar">
                                            <span>{{ substr($station->agent->user->name, 0, 1) }}</span>
                                        </div>
                                        <div class="agent-info">
                                            <div class="agent-name">{{ $station->agent->user->name }}</div>
                                            <div class="agent-phone">{{ $station->agent->user->phone_number }}</div>
                                        </div>
                                    </div>
                                @else
                                    <div class="no-agent">
                                        <i class="bi bi-person-x"></i>
                                        <span>No agent assigned</span>
                                    </div>
                                @endif
                            </td>

                            <td class="td-status">
                                @if($station->agent && $station->agent->votes->count() > 0)
                                    <div class="status-badge status-submitted">
                                        <i class="bi bi-check-circle-fill"></i>
                                        <span>Submitted</span>
                                    </div>
                                @else
                                    <div class="status-badge status-pending">
                                        <i class="bi bi-clock-fill"></i>
                                        <span>Pending</span>
                                    </div>
                                @endif
                            </td>

                            <td class="td-votes">
                                @if($station->agent)
                                    <div class="votes-cell">
                                        <div class="votes-number">{{ number_format($station->agent->votes->sum('number_of_votes')) }}</div>
                                        <div class="votes-label">votes</div>
                                    </div>
                                @else
                                    <div class="no-data">-</div>
                                @endif
                            </td>

                            <td class="td-evidence">
                                @if($station->agent)
                                    @php
                                        // Get latest 2 evidence records for this agent
                                        $latestEvidence = $station->agent->eveidences()
                                            ->latest('created_at')
                                            ->limit(2)
                                            ->get();
                                        $totalEvidenceCount = $station->agent->eveidences->count();
                                    @endphp

                                    @if($totalEvidenceCount > 0)
                                        <div class="evidence-container">
                                            <div class="evidence-summary mb-2">
                                                <div class="evidence-badge evidence-clickable"
                                                     onclick="viewStationEvidence({{ $station->id }})"
                                                     title="Click to view all evidence files">
                                                    <i class="bi bi-camera-fill"></i>
                                                    <span>{{ $totalEvidenceCount }} files</span>
                                                </div>
                                            </div>

                                            <!-- Latest Evidence Preview -->
                                            <div class="latest-evidence-list">
                                                @foreach($latestEvidence as $index => $evidence)
                                                    <div class="evidence-item-compact mb-1">
                                                        <div class="d-flex align-items-center justify-content-between">
                                                            <div class="evidence-info-compact">
                                                                <div class="evidence-name-compact" title="{{ $evidence->file_name ?? 'Evidence File' }}">
                                                                    {{ Str::limit($evidence->file_name ?? 'Evidence File', 15) }}
                                                                    @if($index === 0)
                                                                        <span class="latest-badge">Latest</span>
                                                                    @endif
                                                                </div>
                                                                <div class="evidence-time-compact">
                                                                    {{ $evidence->created_at->format('M d, H:i') }}
                                                                </div>
                                                            </div>
                                                            <div class="evidence-actions-compact">
                                                                <a href="{{ url('files/' . $evidence->file_url) }}"
                                                                   target="_blank"
                                                                   class="btn-evidence-action"
                                                                   title="View {{ $evidence->file_name ?? 'Evidence' }}">
                                                                    <i class="bi bi-eye"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @else
                                        <div class="evidence-badge evidence-empty">
                                            <i class="bi bi-camera"></i>
                                            <span>0 files</span>
                                        </div>
                                    @endif
                                @else
                                    <div class="no-data">-</div>
                                @endif
                            </td>

                            <td class="td-actions">
                                <div class="action-buttons">
                                    <a href="{{ route('manager.vote-form', $station) }}"
                                       class="action-btn action-btn-primary"
                                       title="Submit Votes">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="{{ route('manager.evidence-form', $station) }}"
                                       class="action-btn action-btn-secondary"
                                       title="Upload Evidence">
                                        <i class="bi bi-upload"></i>
                                    </a>
                                    <button class="action-btn action-btn-info"
                                            onclick="viewStationDetails({{ $station->id }})"
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr class="empty-row">
                            <td colspan="7" class="empty-cell">
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="bi bi-inbox"></i>
                                    </div>
                                    <div class="empty-title">No polling stations found</div>
                                    <div class="empty-subtitle">Try adjusting your filters or search criteria</div>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        @if($pollingStations->hasPages())
        <div class="data-table-footer">
            <div class="pagination-info">
                <span class="pagination-text">
                    Showing {{ $pollingStations->firstItem() }} to {{ $pollingStations->lastItem() }}
                    of {{ number_format($pollingStations->total()) }} stations
                </span>
            </div>
            <div class="pagination-controls">
                {{ $pollingStations->links() }}
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Station Details Modal -->
<div class="modal fade" id="stationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Station Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="stationDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    window.location.reload();
}

// View station evidence function
async function viewStationEvidence(stationId) {
    try {
        // Show loading state
        const evidenceBadge = document.querySelector(`[onclick="viewStationEvidence(${stationId})"]`);
        if (evidenceBadge) {
            const originalContent = evidenceBadge.innerHTML;
            evidenceBadge.innerHTML = '<i class="bi bi-hourglass-split"></i> <span>Loading...</span>';
            evidenceBadge.style.pointerEvents = 'none';

            // Restore after timeout
            setTimeout(() => {
                evidenceBadge.innerHTML = originalContent;
                evidenceBadge.style.pointerEvents = 'auto';
            }, 3000);
        }

        const response = await fetch(`/api/station-evidence/${stationId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (response.ok) {
            const data = await response.json();

            if (data.evidence && data.evidence.length > 0) {
                // Open each evidence file in a new tab
                data.evidence.forEach((evidence, index) => {
                    setTimeout(() => {
                        const url = `/files/${evidence.file_url}`;
                        window.open(url, '_blank');
                    }, index * 100); // Small delay between opening tabs to prevent popup blocker
                });

                // Show success message
                showToast(`Opened ${data.evidence.length} evidence file(s) in new tabs`, 'success');
            } else {
                showToast('No evidence files found for this station', 'warning');
            }
        } else {
            throw new Error('Failed to fetch evidence');
        }
    } catch (error) {
        console.error('Error viewing evidence:', error);
        showToast('Failed to view evidence files', 'error');
    }
}

function viewStationDetails(stationId) {
    const modal = new bootstrap.Modal(document.getElementById('stationDetailsModal'));
    const content = document.getElementById('stationDetailsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;

    modal.show();

    // Fetch station details
    fetch(`/manager/station/${stationId}/details`)
        .then(response => response.json())
        .then(data => {
            content.innerHTML = generateStationDetailsHTML(data);
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load station details</div>';
        });
}

// View station evidence function
async function viewStationEvidence(stationId) {
    try {
        // Show loading state
        const evidenceBadge = document.querySelector(`[onclick="viewStationEvidence(${stationId})"]`);
        if (evidenceBadge) {
            const originalContent = evidenceBadge.innerHTML;
            evidenceBadge.innerHTML = '<i class="bi bi-hourglass-split"></i> <span>Loading...</span>';
            evidenceBadge.style.pointerEvents = 'none';

            // Restore after timeout
            setTimeout(() => {
                evidenceBadge.innerHTML = originalContent;
                evidenceBadge.style.pointerEvents = 'auto';
            }, 3000);
        }

        const response = await fetch(`/api/station-evidence/${stationId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (response.ok) {
            const data = await response.json();

            if (data.evidence && data.evidence.length > 0) {
                // Open each evidence file in a new tab
                data.evidence.forEach((evidence, index) => {
                    setTimeout(() => {
                        const url = `/files/${evidence.file_url}`;
                        window.open(url, '_blank');
                    }, index * 100); // Small delay between opening tabs to prevent popup blocker
                });

                // Show success message
                showToast(`Opened ${data.evidence.length} evidence file(s) in new tabs`, 'success');
            } else {
                showToast('No evidence files found for this station', 'warning');
            }
        } else {
            throw new Error('Failed to fetch evidence');
        }
    } catch (error) {
        console.error('Error viewing evidence:', error);
        showToast('Failed to view evidence files', 'error');
    }
}

// Simple toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} toast-notification`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    `;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

function generateStationDetailsHTML(data) {
    const station = data.station;
    const voteSummary = data.vote_summary;

    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Station Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${station.name}</td></tr>
                    <tr><td><strong>District:</strong></td><td>${station.district}</td></tr>
                    <tr><td><strong>County:</strong></td><td>${station.county}</td></tr>
                    <tr><td><strong>Subcounty:</strong></td><td>${station.subcounty}</td></tr>
                    <tr><td><strong>Parish:</strong></td><td>${station.parish || 'N/A'}</td></tr>
                    <tr><td><strong>Village:</strong></td><td>${station.village || 'N/A'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Agent Information</h6>
                ${station.agent ? `
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${station.agent.user.name}</td></tr>
                        <tr><td><strong>Phone:</strong></td><td>${station.agent.user.phone_number}</td></tr>
                        <tr><td><strong>Evidence Files:</strong></td><td>${data.evidence_count}</td></tr>
                    </table>
                ` : '<p class="text-muted">No agent assigned</p>'}
            </div>
        </div>
    `;

    if (Object.keys(voteSummary).length > 0) {
        html += '<hr><h6>Vote Summary</h6>';
        for (const [position, summary] of Object.entries(voteSummary)) {
            html += `
                <div class="mb-3">
                    <strong>${position}</strong> (Total: ${summary.total_votes})
                    <ul class="list-unstyled ms-3">
            `;
            summary.candidates.forEach(candidate => {
                html += `<li>${candidate.name}: ${candidate.votes} votes</li>`;
            });
            html += '</ul></div>';
        }
    }

    return html;
}

// Simple toast notification function
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} toast-notification`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    `;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add animation styles if not already added
    if (!document.getElementById('toast-animations')) {
        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}
</script>
@endsection

@section('styles')
<style>
/* World-Class Manager Dashboard Styles */
:root {
    --primary-gold: #FFD700;
    --primary-gold-dark: #FFA500;
    --success-green: #10B981;
    --warning-orange: #F59E0B;
    --info-blue: #3B82F6;
    --danger-red: #EF4444;
    --neutral-50: #F9FAFB;
    --neutral-100: #F3F4F6;
    --neutral-200: #E5E7EB;
    --neutral-300: #D1D5DB;
    --neutral-400: #9CA3AF;
    --neutral-500: #6B7280;
    --neutral-600: #4B5563;
    --neutral-700: #374151;
    --neutral-800: #1F2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* Hero Section */
.hero-card {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    border-radius: var(--radius-2xl);
    padding: 3rem 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    margin-bottom: 2rem;
}

.hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    flex: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    max-width: 600px;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-hero-primary {
    background: white;
    color: var(--primary-gold-dark);
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--primary-gold-dark);
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0.875rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
}

.hero-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: -50px;
    right: -50px;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 50px;
    right: 100px;
    background: rgba(255, 255, 255, 0.05);
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: -25px;
    right: 50px;
    background: rgba(255, 255, 255, 0.08);
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-card-body {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, var(--success-green) 0%, #059669 100%);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-orange) 0%, #D97706 100%);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, var(--info-blue) 0%, #2563EB 100%);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--neutral-900);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--neutral-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.stat-card-footer {
    display: flex;
    justify-content: flex-end;
}

.stat-percentage {
    text-align: center;
    padding: 0.75rem;
}

.percentage-text {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 0.25rem;
}

.stat-card-success .percentage-text {
    color: var(--success-green);
}

.stat-card-warning .percentage-text {
    color: var(--warning-orange);
}

.stat-card-info .percentage-text {
    color: var(--info-blue);
}

.percentage-label {
    font-size: 0.75rem;
    color: var(--neutral-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

/* Ultra Compact Filter Section */
.ultra-compact-filter-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-200);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.ultra-compact-filter-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-title-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--neutral-800);
}

.filter-title-compact i {
    color: var(--primary-gold-dark);
    font-size: 1rem;
}

.filter-count-badge {
    background: var(--primary-gold);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.25rem;
}

.filter-quick-actions {
    display: flex;
    gap: 0.25rem;
}

.btn-quick-action {
    width: 1.75rem;
    height: 1.75rem;
    border: none;
    background: var(--neutral-100);
    color: var(--neutral-600);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-quick-action:hover {
    background: var(--neutral-200);
    color: var(--neutral-700);
    transform: scale(1.05);
}

.filter-row-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 100px;
    position: relative;
}

.filter-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--neutral-400);
    font-size: 0.875rem;
    z-index: 2;
}

.form-control-ultra-compact {
    width: 100%;
    padding: 0.375rem 0.5rem;
    padding-left: 2rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    background: white;
    transition: all 0.2s ease;
    height: 2rem;
}

.form-control-ultra-compact:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.1);
}

.form-control-ultra-compact::placeholder {
    color: var(--neutral-400);
    font-size: 0.8rem;
}

.form-control-ultra-compact option {
    padding: 0.25rem 0.5rem;
}

.filter-actions-ultra-compact {
    flex-shrink: 0;
}

.btn-filter-apply {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-filter-apply:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.active-filters-display {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--neutral-200);
}

.active-filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--primary-gold);
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.active-filter-remove {
    background: none;
    border: none;
    color: white;
    font-size: 0.75rem;
    cursor: pointer;
    padding: 0;
    margin-left: 0.25rem;
}

.active-filter-remove:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Data Table Section */
.data-table-section {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.data-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: var(--neutral-50);
    border-bottom: 1px solid var(--neutral-200);
}

.data-table-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.title-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neutral-900);
    margin: 0;
}

.title-content p {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin: 0;
}

.data-table-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.table-stats {
    display: flex;
    gap: 0.75rem;
}

.stat-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--neutral-100);
    color: var(--neutral-700);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-table-action {
    background: var(--neutral-100);
    color: var(--neutral-600);
    border: 1px solid var(--neutral-300);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-table-action:hover {
    background: var(--neutral-200);
    color: var(--neutral-700);
}

.btn-table-action.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: white;
    border-color: transparent;
}

.btn-table-action.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Data Table */
.data-table-container {
    overflow-x: auto;
}

.table-wrapper {
    min-width: 800px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.table-header th {
    background: var(--neutral-50);
    border-bottom: 2px solid var(--neutral-200);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--neutral-700);
    font-size: 0.875rem;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.th-content i {
    color: var(--primary-gold-dark);
}

.table-body .table-row {
    border-bottom: 1px solid var(--neutral-200);
    transition: all 0.2s ease;
}

.table-body .table-row:hover {
    background: var(--neutral-50);
}

.table-body td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
}

/* Table Cell Styles */
.station-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.station-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.station-info {
    flex: 1;
}

.station-name {
    font-weight: 600;
    color: var(--neutral-900);
    font-size: 0.875rem;
}

.station-id {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.location-cell {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.location-primary {
    font-weight: 600;
    color: var(--neutral-900);
    font-size: 0.875rem;
}

.location-secondary {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.location-tertiary {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.7rem;
    color: var(--neutral-400);
    font-style: italic;
    margin-top: 0.125rem;
}

.location-tertiary i {
    font-size: 0.65rem;
    color: var(--primary-gold);
}

.agent-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.agent-avatar {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, var(--info-blue) 0%, #2563EB 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.agent-info {
    flex: 1;
}

.agent-name {
    font-weight: 500;
    color: var(--neutral-900);
    font-size: 0.875rem;
}

.agent-phone {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.no-agent {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--neutral-400);
    font-size: 0.875rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.status-submitted {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-green);
}

.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-orange);
    animation: pulse 2s infinite;
}

.votes-cell {
    text-align: center;
}

.votes-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-gold-dark);
}

.votes-label {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.evidence-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-blue);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
}

.evidence-clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.evidence-clickable:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.3);
}

.evidence-empty {
    background: rgba(156, 163, 175, 0.1);
    color: var(--neutral-400);
}

.evidence-container {
    min-width: 200px;
}

.evidence-summary {
    display: flex;
    justify-content: center;
}

.latest-evidence-list {
    max-height: 120px;
    overflow-y: auto;
}

.evidence-item-compact {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 6px;
    padding: 6px 8px;
    font-size: 0.75rem;
}

.evidence-info-compact {
    flex-grow: 1;
    min-width: 0;
}

.evidence-name-compact {
    font-weight: 600;
    color: var(--neutral-700);
    display: flex;
    align-items: center;
    gap: 4px;
}

.latest-badge {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    font-size: 0.6rem;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
}

.evidence-time-compact {
    color: var(--neutral-500);
    font-size: 0.65rem;
    margin-top: 1px;
}

.evidence-actions-compact {
    display: flex;
    gap: 2px;
}

.btn-evidence-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-blue);
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.btn-evidence-action:hover {
    background: rgba(59, 130, 246, 0.2);
    color: var(--info-blue);
    transform: scale(1.1);
}

.no-data {
    color: var(--neutral-400);
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border-radius: var(--radius-md);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    text-decoration: none;
}

.action-btn-primary {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-dark) 100%);
    color: white;
}

.action-btn-secondary {
    background: var(--neutral-100);
    color: var(--neutral-600);
    border: 1px solid var(--neutral-300);
}

.action-btn-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-blue);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.empty-cell {
    padding: 4rem 2rem;
    text-align: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.empty-icon {
    font-size: 4rem;
    color: var(--neutral-300);
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-600);
}

.empty-subtitle {
    font-size: 0.875rem;
    color: var(--neutral-500);
}

/* Pagination */
.data-table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--neutral-50);
    border-top: 1px solid var(--neutral-200);
}

.pagination-text {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-card,
.filter-section,
.data-table-section {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .filter-row-compact {
        flex-wrap: wrap;
        gap: 0.375rem;
    }

    .filter-group {
        min-width: 120px;
        flex: 1 1 calc(50% - 0.25rem);
    }

    .data-table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .hero-card {
        padding: 2rem 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        justify-content: center;
    }

    .ultra-compact-filter-section {
        padding: 0.5rem 0.75rem;
    }

    .filter-row-compact {
        flex-direction: column;
        gap: 0.375rem;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
        flex: none;
    }

    .form-control-ultra-compact {
        height: 2.25rem;
        font-size: 0.875rem;
    }

    .filter-actions-ultra-compact {
        align-self: center;
        margin-top: 0.25rem;
    }

    .btn-filter-apply {
        width: 2.5rem;
        height: 2.25rem;
    }

    .data-table-header {
        padding: 1.5rem;
    }

    .table-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-table-action {
        width: 100%;
        justify-content: center;
    }

    .active-filters-display {
        flex-direction: column;
        gap: 0.375rem;
    }
}

@media (max-width: 640px) {
    .stat-card {
        padding: 1rem;
    }

    .stat-card-body {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-number {
        font-size: 1.875rem;
    }

    .filter-header,
    .filter-body {
        padding: 1rem;
    }

    .data-table-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
}


</style>
@endsection

@section('scripts')
<script>
console.log('JavaScript is loading...');

// Ultra Compact Filter Functions
function clearAllFilters() {
    try {
        const form = document.getElementById('filterForm');
        const inputs = form.querySelectorAll('input, select');

        inputs.forEach(input => {
            if (input.type === 'text') {
                input.value = '';
            } else if (input.tagName === 'SELECT') {
                input.selectedIndex = 0;
            }
        });

        // Submit form to apply changes
        form.submit();
    } catch (error) {
        console.error('Error clearing filters:', error);
        window.location.href = window.location.pathname;
    }
}

function toggleFilterExpand() {
    const filterRow = document.getElementById('filterRow');
    const expandIcon = document.getElementById('expandIcon');
    const activeFilters = document.getElementById('activeFilters');

    if (filterRow.style.display === 'none') {
        filterRow.style.display = 'flex';
        expandIcon.className = 'bi bi-chevron-down';
        activeFilters.style.display = 'none';
    } else {
        filterRow.style.display = 'none';
        expandIcon.className = 'bi bi-chevron-up';
        updateActiveFiltersDisplay();
        activeFilters.style.display = 'flex';
    }
}

function updateActiveFiltersDisplay() {
    const activeFilters = document.getElementById('activeFilters');
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    let activeFiltersHTML = '';

    for (let [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            const displayName = key.charAt(0).toUpperCase() + key.slice(1);
            activeFiltersHTML += `
                <div class="active-filter-tag">
                    <span>${displayName}: ${value}</span>
                    <button type="button" class="active-filter-remove" onclick="removeFilter('${key}')">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            `;
        }
    }

    if (activeFiltersHTML === '') {
        activeFiltersHTML = '<span class="text-muted" style="font-size: 0.75rem;">No active filters</span>';
    }

    activeFilters.innerHTML = activeFiltersHTML;
}

function removeFilter(filterName) {
    const element = document.getElementById(filterName);
    if (element) {
        if (element.tagName === 'SELECT') {
            element.selectedIndex = 0;
        } else {
            element.value = '';
        }
        document.getElementById('filterForm').submit();
    }
}

// Legacy function for backward compatibility
function resetFilters() {
    clearAllFilters();
}

// Initialize Ultra Compact Filters
function initializeUltraCompactFilters() {
    // Add search debouncing
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);

            // Add loading indicator
            const icon = this.parentElement.querySelector('.filter-icon');
            if (icon) {
                icon.className = 'bi bi-hourglass-split filter-icon';
            }

            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 800);
        });

        // Restore search icon on focus out
        searchInput.addEventListener('blur', function() {
            const icon = this.parentElement.querySelector('.filter-icon');
            if (icon) {
                icon.className = 'bi bi-search filter-icon';
            }
        });
    }

    // Initialize active filters display if collapsed
    const filterRow = document.getElementById('filterRow');
    if (filterRow && filterRow.style.display === 'none') {
        updateActiveFiltersDisplay();
    }

    // Auto-collapse filters on mobile
    if (window.innerWidth < 768) {
        const filterRow = document.getElementById('filterRow');
        const expandIcon = document.getElementById('expandIcon');
        const activeFilters = document.getElementById('activeFilters');

        if (filterRow && expandIcon && activeFilters) {
            filterRow.style.display = 'none';
            expandIcon.className = 'bi bi-chevron-up';
            updateActiveFiltersDisplay();
            activeFilters.style.display = 'flex';
        }
    }
}

// Verify function is accessible
console.log('resetFilters function defined:', typeof window.resetFilters);

// Dashboard functionality

// World-Class Dashboard Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();

    // Initialize ultra compact filters
    initializeUltraCompactFilters();

    // Auto-refresh functionality with smart detection
    let autoRefreshInterval;
    let userActivity = false;

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            if (!document.hidden && !userActivity) {
                refreshData();
            }
        }, 300000); // 5 minutes
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    // Track user activity
    ['click', 'keydown', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, () => {
            userActivity = true;
            stopAutoRefresh();

            // Reset after 30 seconds of inactivity
            clearTimeout(window.inactivityTimer);
            window.inactivityTimer = setTimeout(() => {
                userActivity = false;
                startAutoRefresh();
            }, 30000);
        });
    });

    // Enhanced search with debouncing
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);

            // Add loading indicator
            const icon = this.parentElement.querySelector('.filter-input-icon i');
            if (icon) {
                icon.className = 'bi bi-hourglass-split';
            }

            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 800);
        });
    }

    // Enhanced form submissions with loading states
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Loading...';
                submitBtn.disabled = true;

                // Re-enable after timeout (fallback)
                setTimeout(() => {
                    submitBtn.innerHTML = originalContent;
                    submitBtn.disabled = false;
                }, 10000);
            }
        });
    });

    // Animate statistics cards on load
    animateStatCards();

    // Initialize tooltips
    initializeTooltips();

    // Start auto-refresh
    startAutoRefresh();
});

// Initialize dashboard components
function initializeDashboard() {
    // Add smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';

    // Initialize progress rings
    initializeProgressRings();

    // Debug Laravel data
    console.log('=== Laravel Data Debug ===');
    console.log('Total Stations: {{ $totalStations }}');
    console.log('Submitted Stations: {{ $submittedStations }}');
    console.log('Pending Stations: {{ $pendingStations }}');
    console.log('Total Votes: {{ $totalVotes }}');

    // Debug progress rings
    console.log('=== Progress Rings Debug ===');
    document.querySelectorAll('.progress-ring').forEach((ring, index) => {
        const circle = ring.querySelector('.progress-ring-circle');
        const type = ring.getAttribute('data-type');
        const total = circle.getAttribute('data-total');
        const value = circle.getAttribute('data-value');
        const percentage = circle.getAttribute('data-percentage');

        console.log(`Ring ${index + 1} (${type}): ${value}/${total} = ${percentage}%`);
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshData();
        }

        // Ctrl/Cmd + E for export
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
            e.preventDefault();
            exportData();
        }
    });
}

// Simple working progress rings
function initializeProgressRings() {
    console.log('Initializing progress rings...');

    const progressRings = document.querySelectorAll('.progress-ring');
    console.log(`Found ${progressRings.length} progress rings`);

    progressRings.forEach((ring, index) => {
        const circle = ring.querySelector('.progress-ring-circle');
        const textElement = ring.querySelector('.progress-ring-text');
        const type = ring.getAttribute('data-type');

        if (!circle || !textElement) {
            console.log(`Missing elements for ring ${index}`);
            return;
        }

        // Get percentage from data attribute (handle decimals)
        let percentage = parseFloat(circle.getAttribute('data-percentage')) || 0;

        console.log(`Ring ${index} (${type}): ${percentage}%`);

        // Calculate progress ring values
        const radius = 25;
        const circumference = 2 * Math.PI * radius; // ≈ 157.08
        const offset = circumference - (percentage / 100) * circumference;

        console.log(`Ring ${index} calculations:`);
        console.log(`  Percentage: ${percentage}%`);
        console.log(`  Circumference: ${circumference.toFixed(2)}`);
        console.log(`  Target offset: ${offset.toFixed(2)}`);
        console.log(`  Progress fill: ${((percentage / 100) * circumference).toFixed(2)}`);

        // Set up circle
        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = circumference; // Start empty
        circle.style.transition = 'none';

        // Keep the text as set by Laravel (don't override)
        console.log(`Text element content: "${textElement.textContent}" or "${textElement.innerHTML}"`);

        // Only update votes icon if needed
        if (type === 'votes' && !textElement.innerHTML.includes('bi-infinity')) {
            textElement.innerHTML = '<i class="bi bi-infinity"></i>';
        }

        // Animate after delay
        setTimeout(() => {
            circle.style.transition = 'stroke-dashoffset 1.5s ease-out';
            circle.style.strokeDashoffset = offset;
            console.log(`✓ Animated ring ${index} (${type}) to ${percentage}%`);
        }, 800 + (index * 300));
    });
}

// Animate statistics cards
function animateStatCards() {
    const cards = document.querySelectorAll('.stat-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            new bootstrap.Tooltip(element);
        }
    });
}

// Enhanced export functionality
function exportData() {
    const exportBtn = event.target.closest('.btn-table-action, .btn-hero-secondary');
    if (!exportBtn) return;

    const originalContent = exportBtn.innerHTML;

    // Show loading state
    exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Exporting...';
    exportBtn.disabled = true;

    // Simulate export process with progress
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += 20;
        exportBtn.innerHTML = `<i class="bi bi-hourglass-split"></i> ${progress}%`;

        if (progress >= 100) {
            clearInterval(progressInterval);
            exportBtn.innerHTML = '<i class="bi bi-check-circle"></i> Exported!';

            // Reset after 2 seconds
            setTimeout(() => {
                exportBtn.innerHTML = originalContent;
                exportBtn.disabled = false;
            }, 2000);
        }
    }, 300);

    // Here you would implement actual export functionality
    console.log('Exporting polling station data...');
}

// Enhanced refresh with visual feedback
function refreshData() {
    const refreshBtns = document.querySelectorAll('[onclick*="refreshData"]');

    refreshBtns.forEach(btn => {
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refreshing...';
        btn.disabled = true;

        // Add spinning animation
        const icon = btn.querySelector('i');
        if (icon) {
            icon.style.animation = 'spin 1s linear infinite';
        }
    });

    // Add page-wide loading indicator
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-content">
            <div class="spinner-border text-warning" style="width: 3rem; height: 3rem;"></div>
            <div class="mt-3 text-muted">Refreshing data...</div>
        </div>
    `;
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
    `;

    document.body.appendChild(loadingOverlay);

    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// Reset filters functionality
function resetFilters() {
    const form = document.querySelector('.filter-form');
    if (!form) return;

    // Clear all form inputs
    form.querySelectorAll('input, select').forEach(input => {
        input.value = '';
    });

    // Add visual feedback
    const resetBtn = event.target;
    const originalContent = resetBtn.innerHTML;
    resetBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Resetting...';
    resetBtn.disabled = true;

    setTimeout(() => {
        form.submit();
    }, 500);
}

// Enhanced station details modal
function viewStationDetails(stationId) {
    const modal = new bootstrap.Modal(document.getElementById('stationDetailsModal'));
    const content = document.getElementById('stationDetailsContent');

    // Enhanced loading animation
    content.innerHTML = `
        <div class="text-center py-5">
            <div class="loading-animation">
                <div class="spinner-border" style="width: 3rem; height: 3rem; color: var(--primary-gold);">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="mt-3 text-muted">Loading station details...</div>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar" style="width: 0%; background: var(--primary-gold);"></div>
                </div>
            </div>
        </div>
    `;

    modal.show();

    // Simulate loading progress
    const progressBar = content.querySelector('.progress-bar');
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += 25;
        progressBar.style.width = progress + '%';

        if (progress >= 100) {
            clearInterval(progressInterval);

            // Show station details
            setTimeout(() => {
                content.innerHTML = `
                    <div class="station-details">
                        <div class="alert alert-info border-0" style="background: rgba(59, 130, 246, 0.1);">
                            <i class="bi bi-info-circle me-2"></i>
                            Station details functionality will be implemented with backend API.
                        </div>
                        <div class="text-center py-4">
                            <div class="station-preview">
                                <div class="station-icon mb-3">
                                    <i class="bi bi-building" style="font-size: 3rem; color: var(--primary-gold);"></i>
                                </div>
                                <h5 style="color: var(--primary-gold);">Station ID: ${stationId}</h5>
                                <p class="text-muted">Detailed information will be displayed here once the API is connected.</p>
                            </div>
                        </div>
                    </div>
                `;
            }, 300);
        }
    }, 200);
}

// Add CSS for loading overlay
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .loading-content {
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .station-details .station-preview {
        padding: 2rem;
    }

    .station-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 215, 0, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }
`;
document.head.appendChild(style);
</script>
@endsection
