<?php

namespace App\Http\Middleware;

use App\Models\ApiLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class ApiLoggingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);

        // Capture request data
        $requestData = $this->captureRequestData($request);

        // Process the request
        $response = $next($request);

        // Calculate response time
        $endTime = microtime(true);
        $responseTime = round(($endTime - $startTime) * 1000); // Convert to milliseconds

        // Capture response data and log
        $this->logApiRequest($request, $response, $requestData, $responseTime);

        return $response;
    }

    /**
     * Capture request data
     */
    private function captureRequestData(Request $request): array
    {
        return [
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'headers' => $this->sanitizeHeaders($request->headers->all()),
            'query_parameters' => $request->query->all(),
            'request_payload' => $this->captureRequestPayload($request),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'session_id' => $request->hasSession() ? $request->session()->getId() : null,
        ];
    }

    /**
     * Capture request payload (handle file uploads appropriately)
     */
    private function captureRequestPayload(Request $request): ?string
    {
        $payload = $request->except(['password', 'password_confirmation', '_token']);

        // Handle file uploads - don't store actual file content
        if ($request->hasFile('picture') || $request->hasFile('evidence_files')) {
            $files = [];

            if ($request->hasFile('picture')) {
                $file = $request->file('picture');
                $files['picture'] = [
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                ];
            }

            if ($request->hasFile('evidence_files')) {
                $files['evidence_files'] = [];
                foreach ($request->file('evidence_files') as $index => $file) {
                    if ($file && $file->isValid()) {
                        $files['evidence_files'][$index] = [
                            'name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $file->getMimeType(),
                        ];
                    }
                }
            }

            $payload['_files'] = $files;
        }

        return json_encode($payload, JSON_PRETTY_PRINT);
    }

    /**
     * Sanitize headers (remove sensitive information)
     */
    private function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];

        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }

        return $headers;
    }

    /**
     * Determine log type based on endpoint
     */
    private function determineLogType(string $endpoint): string
    {
        if (str_contains($endpoint, 'record_envidence') || str_contains($endpoint, 'evidence')) {
            return 'evidence';
        } elseif (str_contains($endpoint, 'record_votes') || str_contains($endpoint, 'vote')) {
            return 'vote';
        }

        return 'other';
    }

    /**
     * Get user information
     */
    private function getUserInfo(): array
    {
        $user = Auth::user();

        if (!$user) {
            return [
                'user_id' => null,
                'user_type' => null,
                'auth_method' => 'none',
                'api_token_id' => null,
            ];
        }

        $authMethod = 'session';
        $tokenId = null;

        // Check if using Sanctum token
        if (method_exists($user, 'currentAccessToken') && $user->currentAccessToken()) {
            $authMethod = 'sanctum_token';
            $tokenId = $user->currentAccessToken()->id;
        }

        return [
            'user_id' => $user->id,
            'user_type' => $user->user_type,
            'auth_method' => $authMethod,
            'api_token_id' => $tokenId,
        ];
    }

    /**
     * Extract location data from request
     */
    private function extractLocationData(Request $request): ?array
    {
        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');

        if ($latitude && $longitude) {
            return [
                'latitude' => (float) $latitude,
                'longitude' => (float) $longitude,
            ];
        }

        return null;
    }

    /**
     * Log the API request
     */
    private function logApiRequest(Request $request, Response $response, array $requestData, int $responseTime): void
    {
        try {
            $userInfo = $this->getUserInfo();
            $logType = $this->determineLogType($requestData['endpoint']);
            $isSuccessful = $response->getStatusCode() >= 200 && $response->getStatusCode() < 400;

            // Capture response body (limit size for large responses)
            $responseBody = $response->getContent();
            if (strlen($responseBody) > 50000) { // Limit to 50KB
                $responseBody = substr($responseBody, 0, 50000) . '... [TRUNCATED]';
            }

            $logData = array_merge($requestData, $userInfo, [
                'response_status' => $response->getStatusCode(),
                'response_body' => $responseBody,
                'response_headers' => $this->sanitizeHeaders($response->headers->all()),
                'response_time_ms' => $responseTime,
                'location_data' => $this->extractLocationData($request),
                'log_type' => $logType,
                'is_successful' => $isSuccessful,
                'error_message' => $isSuccessful ? null : $this->extractErrorMessage($response),
                'metadata' => [
                    'request_size' => strlen($requestData['request_payload'] ?? ''),
                    'response_size' => strlen($responseBody),
                    'timestamp' => Carbon::now()->toISOString(),
                ],
            ]);

            // Create the log entry
            $apiLog = ApiLog::create($logData);

            // Auto-flag if suspicious
            $apiLog->autoFlag();

        } catch (\Exception $e) {
            // Log the error but don't break the request
            Log::error('Failed to log API request', [
                'error' => $e->getMessage(),
                'endpoint' => $requestData['endpoint'] ?? 'unknown',
                'method' => $requestData['method'] ?? 'unknown',
            ]);
        }
    }

    /**
     * Extract error message from response
     */
    private function extractErrorMessage(Response $response): ?string
    {
        $content = $response->getContent();

        if (empty($content)) {
            return 'Empty response';
        }

        $decoded = json_decode($content, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded['message'] ?? $decoded['error'] ?? 'API error occurred';
        }

        return 'Non-JSON error response';
    }
}
