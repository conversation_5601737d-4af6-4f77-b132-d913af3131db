<?php

namespace App\Http\Controllers;

use App\Models\ApiLog;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ApiLogController extends Controller
{
    /**
     * Display the API logs monitoring dashboard
     */
    public function index(Request $request)
    {
        $query = ApiLog::with(['user', 'flaggedBy'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('log_type')) {
            $query->where('log_type', $request->log_type);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'success') {
                $query->where('is_successful', true);
            } elseif ($request->status === 'error') {
                $query->where('is_successful', false);
            }
        }

        if ($request->filled('flagged')) {
            $query->where('is_flagged', $request->flagged === '1');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('endpoint')) {
            $query->where('endpoint', 'like', '%' . $request->endpoint . '%');
        }

        // Get page size from request (default 25, max 100)
        $perPage = min((int) $request->get('per_page', 25), 100);
        $logs = $query->paginate($perPage)->withQueryString();

        // Get summary statistics
        $stats = $this->getApiLogStats();

        // Get filter options
        $users = User::whereIn('id', ApiLog::distinct()->pluck('user_id')->filter())
            ->get(['id', 'name', 'user_type']);

        return view('admin.api-logs.index', compact('logs', 'stats', 'users'));
    }

    /**
     * Show detailed view of a specific API log
     */
    public function show(ApiLog $apiLog)
    {
        $apiLog->load(['user', 'flaggedBy']);

        return view('admin.api-logs.show', compact('apiLog'));
    }

    /**
     * Flag an API log for review
     */
    public function flag(Request $request, ApiLog $apiLog)
    {
        $request->validate([
            'reason' => 'required|string|max:255',
        ]);

        $apiLog->update([
            'is_flagged' => true,
            'flag_reason' => $request->reason,
            'flagged_at' => now(),
            'flagged_by_user_id' => auth()->id(),
        ]);

        return redirect()->back()->with('success', 'API log flagged successfully.');
    }

    /**
     * Unflag an API log
     */
    public function unflag(ApiLog $apiLog)
    {
        $apiLog->update([
            'is_flagged' => false,
            'flag_reason' => null,
            'flagged_at' => null,
            'flagged_by_user_id' => null,
        ]);

        return redirect()->back()->with('success', 'API log unflagged successfully.');
    }

    /**
     * Get API log statistics
     */
    private function getApiLogStats(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $lastWeek = Carbon::now()->subWeek();

        return [
            'total_requests' => ApiLog::count(),
            'today_requests' => ApiLog::whereDate('created_at', $today)->count(),
            'yesterday_requests' => ApiLog::whereDate('created_at', $yesterday)->count(),
            'week_requests' => ApiLog::where('created_at', '>=', $lastWeek)->count(),

            'success_rate' => $this->calculateSuccessRate(),
            'average_response_time' => ApiLog::whereNotNull('response_time_ms')->avg('response_time_ms'),

            'evidence_requests' => ApiLog::evidence()->count(),
            'vote_requests' => ApiLog::vote()->count(),

            'flagged_requests' => ApiLog::flagged()->count(),
            'error_requests' => ApiLog::failed()->count(),

            'unique_users' => ApiLog::distinct('user_id')->count('user_id'),
            'recent_activity' => $this->getRecentActivity(),
        ];
    }

    /**
     * Calculate success rate percentage
     */
    private function calculateSuccessRate(): float
    {
        $total = ApiLog::count();
        if ($total === 0) return 100.0;

        $successful = ApiLog::successful()->count();
        return round(($successful / $total) * 100, 2);
    }

    /**
     * Get recent activity summary
     */
    private function getRecentActivity(): array
    {
        $lastHour = Carbon::now()->subHour();
        $last5Minutes = Carbon::now()->subMinutes(5);

        return [
            'last_hour' => ApiLog::where('created_at', '>=', $lastHour)->count(),
            'last_5_minutes' => ApiLog::where('created_at', '>=', $last5Minutes)->count(),
            'active_users_last_hour' => ApiLog::where('created_at', '>=', $lastHour)
                ->distinct('user_id')->count('user_id'),
        ];
    }

    /**
     * Export API logs as JSON
     */
    public function export(Request $request)
    {
        $query = ApiLog::with(['user']);

        // Apply same filters as index
        if ($request->filled('log_type')) {
            $query->where('log_type', $request->log_type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->orderBy('created_at', 'desc')->get();

        $filename = 'api_logs_' . now()->format('Y-m-d_H-i-s') . '.json';

        return response()->json($logs, 200, [
            'Content-Type' => 'application/json',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    /**
     * Get API statistics for dashboard widgets (AJAX)
     */
    public function stats()
    {
        return response()->json($this->getApiLogStats());
    }

    /**
     * Get real-time activity data (AJAX)
     */
    public function activity()
    {
        $recentLogs = ApiLog::with(['user'])
            ->where('created_at', '>=', Carbon::now()->subMinutes(30))
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json($recentLogs);
    }

    /**
     * Bulk actions on multiple logs
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:flag,unflag,delete',
            'log_ids' => 'required|array|min:1',
            'log_ids.*' => 'exists:api_logs,id',
            'reason' => 'required_if:action,flag|string|max:255',
        ]);

        $logIds = $request->log_ids;
        $action = $request->action;
        $count = 0;

        switch ($action) {
            case 'flag':
                $count = ApiLog::whereIn('id', $logIds)->update([
                    'is_flagged' => true,
                    'flag_reason' => $request->reason,
                    'flagged_at' => now(),
                    'flagged_by_user_id' => auth()->id(),
                ]);
                break;

            case 'unflag':
                $count = ApiLog::whereIn('id', $logIds)->update([
                    'is_flagged' => false,
                    'flag_reason' => null,
                    'flagged_at' => null,
                    'flagged_by_user_id' => null,
                ]);
                break;

            case 'delete':
                $count = ApiLog::whereIn('id', $logIds)->delete();
                break;
        }

        return redirect()->back()->with('success', "Bulk action completed on {$count} logs.");
    }
}
