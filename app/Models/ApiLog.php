<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ApiLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'endpoint',
        'method',
        'url',
        'headers',
        'request_payload',
        'query_parameters',
        'response_status',
        'response_body',
        'response_headers',
        'response_time_ms',
        'user_id',
        'user_type',
        'auth_method',
        'api_token_id',
        'ip_address',
        'user_agent',
        'session_id',
        'location_data',
        'log_type',
        'is_successful',
        'error_message',
        'metadata',
        'is_flagged',
        'flag_reason',
        'flagged_at',
        'flagged_by_user_id',
    ];

    protected $casts = [
        'headers' => 'array',
        'query_parameters' => 'array',
        'response_headers' => 'array',
        'location_data' => 'array',
        'metadata' => 'array',
        'is_successful' => 'boolean',
        'is_flagged' => 'boolean',
        'flagged_at' => 'datetime',
    ];

    /**
     * Get the user that made the API request
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who flagged this log
     */
    public function flaggedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'flagged_by_user_id');
    }

    /**
     * Scope for evidence API logs
     */
    public function scopeEvidence($query)
    {
        return $query->where('log_type', 'evidence');
    }

    /**
     * Scope for vote API logs
     */
    public function scopeVote($query)
    {
        return $query->where('log_type', 'vote');
    }

    /**
     * Scope for successful requests
     */
    public function scopeSuccessful($query)
    {
        return $query->where('is_successful', true);
    }

    /**
     * Scope for failed requests
     */
    public function scopeFailed($query)
    {
        return $query->where('is_successful', false);
    }

    /**
     * Scope for flagged logs
     */
    public function scopeFlagged($query)
    {
        return $query->where('is_flagged', true);
    }

    /**
     * Scope for recent logs (last 24 hours)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDay());
    }

    /**
     * Scope for logs within date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get formatted response time
     */
    public function getFormattedResponseTimeAttribute(): string
    {
        if (!$this->response_time_ms) {
            return 'N/A';
        }

        if ($this->response_time_ms < 1000) {
            return $this->response_time_ms . 'ms';
        }

        return round($this->response_time_ms / 1000, 2) . 's';
    }

    /**
     * Get status badge color for UI
     */
    public function getStatusBadgeColorAttribute(): string
    {
        if (!$this->is_successful) {
            return 'danger';
        }

        if ($this->response_status >= 200 && $this->response_status < 300) {
            return 'success';
        } elseif ($this->response_status >= 300 && $this->response_status < 400) {
            return 'info';
        } elseif ($this->response_status >= 400 && $this->response_status < 500) {
            return 'warning';
        } else {
            return 'danger';
        }
    }

    /**
     * Get endpoint display name
     */
    public function getEndpointDisplayNameAttribute(): string
    {
        switch ($this->log_type) {
            case 'evidence':
                return 'Evidence API';
            case 'vote':
                return 'Vote API';
            default:
                return ucfirst($this->endpoint);
        }
    }

    /**
     * Check if this log should be flagged for review
     */
    public function shouldBeFlagged(): array
    {
        $flags = [];

        // Check for multiple rapid requests from same user
        if ($this->user_id) {
            $recentRequests = self::where('user_id', $this->user_id)
                ->where('endpoint', $this->endpoint)
                ->where('created_at', '>=', Carbon::now()->subMinutes(5))
                ->count();

            if ($recentRequests > 10) {
                $flags[] = 'rapid_requests';
            }
        }

        // Check for unusual response times
        if ($this->response_time_ms && $this->response_time_ms > 10000) {
            $flags[] = 'slow_response';
        }

        // Check for error responses
        if (!$this->is_successful || $this->response_status >= 400) {
            $flags[] = 'error_response';
        }

        // Check for large payloads
        if ($this->request_payload && strlen($this->request_payload) > 1000000) {
            $flags[] = 'large_payload';
        }

        return $flags;
    }

    /**
     * Auto-flag this log if it meets suspicious criteria
     */
    public function autoFlag(): void
    {
        $flags = $this->shouldBeFlagged();

        if (!empty($flags)) {
            $this->update([
                'is_flagged' => true,
                'flag_reason' => $flags[0],
                'flagged_at' => now()
            ]);
        }
    }
}
