<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Eveidence extends Model
{
    use HasFactory;

    protected $table = 'eveidences';

    protected $fillable = [
        'agent_id',
        'file_url',
        'file_name',
    ];

    /**
     * Get the agent that owns the evidence.
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }
}
