<?php

use App\Http\Controllers\EveidenceController;
use App\Http\Controllers\VoteApiController;
use App\Http\Controllers\VoteController;
use Illuminate\Support\Facades\Route;

Route::post('login',[VoteApiController::class,'login']);

Route::group(['middleware' => 'auth:sanctum'], function () { 

    Route::post('logout', [VoteApiController::class, 'logout']);

    Route::post('change_password',[VoteApiController::class,'changePassword']);

    Route::get('user',[VoteApiController::class,'user']);

    Route::get('load_candidates',[VoteApiController::class,'loadCandidates']);    

    // Apply API logging to vote recording endpoint
    Route::post('record_votes',[VoteController::class,'store'])->middleware('api.log');

    Route::post('view_candidates',[VoteApiController::class,'candidateVotes']);

    // Apply API logging to evidence endpoints
    Route::apiResource('record_envidence',EveidenceController::class)->middleware('api.log');

    // Diagnostic endpoint for production debugging
    Route::get('record_envidence/diagnostic', [EveidenceController::class, 'diagnostic'])->middleware('api.log');
    
});
