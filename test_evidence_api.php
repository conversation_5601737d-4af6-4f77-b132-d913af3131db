<?php

/**
 * Manual Evidence API Test Script
 * 
 * This script demonstrates how to interact with the Evidence API endpoints
 * including login, upload, view, and delete operations.
 */

require_once 'vendor/autoload.php';

class EvidenceApiTester
{
    private $baseUrl;
    public $token;

    public function __construct($baseUrl = 'http://localhost:8000')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * <PERSON>gin and get authentication token
     */
    public function login($phoneNumber, $password)
    {
        $url = $this->baseUrl . '/api/login';
        
        $data = [
            'phone_number' => $phoneNumber,
            'password' => $password
        ];

        $response = $this->makeRequest('POST', $url, $data);

        if ($response && isset($response['authorization']['token'])) {
            $this->token = $response['authorization']['token'];
            echo "✅ Login successful! Token: " . substr($this->token, 0, 20) . "...\n";
            return true;
        } else {
            echo "❌ Login failed: " . json_encode($response) . "\n";
            return false;
        }
    }

    /**
     * Upload evidence image
     */
    public function uploadEvidence($imagePath, $fileName = null)
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        if (!file_exists($imagePath)) {
            echo "❌ Image file not found: $imagePath\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence';
        
        $postData = [
            'picture' => new CURLFile($imagePath, mime_content_type($imagePath), basename($imagePath))
        ];

        if ($fileName) {
            $postData['file_name'] = $fileName;
        }

        $response = $this->makeRequest('POST', $url, $postData, true);
        
        if ($response && $response['status'] === 'success') {
            echo "✅ Evidence uploaded successfully!\n";
            echo "   File URL: " . $response['evedence']['file_url'] . "\n";
            echo "   File Name: " . ($response['evedence']['file_name'] ?? 'N/A') . "\n";
            echo "   Evidence ID: " . $response['evedence']['id'] . "\n";
            return $response['evedence'];
        } else {
            echo "❌ Evidence upload failed: " . json_encode($response) . "\n";
            return false;
        }
    }

    /**
     * View all evidence for the authenticated agent
     */
    public function viewEvidence()
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence';
        
        $response = $this->makeRequest('GET', $url);
        
        if ($response && $response['status'] === 'success') {
            echo "✅ Evidence retrieved successfully!\n";
            echo "   Total files: " . count($response['evedence']) . "\n";
            
            foreach ($response['evedence'] as $index => $evidence) {
                echo "   Evidence #" . ($index + 1) . ":\n";
                echo "     ID: " . $evidence['id'] . "\n";
                echo "     File URL: " . $evidence['file_url'] . "\n";
                echo "     File Name: " . ($evidence['file_name'] ?? 'N/A') . "\n";
                echo "     Created: " . $evidence['created_at'] . "\n";
                echo "\n";
            }
            
            return $response['evedence'];
        } else {
            echo "❌ Failed to retrieve evidence: " . json_encode($response) . "\n";
            return false;
        }
    }

    /**
     * Delete evidence by ID
     */
    public function deleteEvidence($evidenceId)
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence/' . $evidenceId;
        
        $response = $this->makeRequest('DELETE', $url);
        
        if ($response && $response['status'] === 'success') {
            echo "✅ Evidence deleted successfully!\n";
            return true;
        } else {
            echo "❌ Failed to delete evidence: " . json_encode($response) . "\n";
            return false;
        }
    }

    /**
     * Create a test image file
     */
    public function createTestImage($filename = 'test_evidence.jpg')
    {
        // Create a simple test image
        $image = imagecreate(400, 300);
        $backgroundColor = imagecolorallocate($image, 255, 255, 255);
        $textColor = imagecolorallocate($image, 0, 0, 0);
        
        imagestring($image, 5, 50, 100, 'TEST EVIDENCE', $textColor);
        imagestring($image, 3, 50, 150, 'Generated: ' . date('Y-m-d H:i:s'), $textColor);
        
        imagejpeg($image, $filename);
        imagedestroy($image);
        
        echo "✅ Test image created: $filename\n";
        return $filename;
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = null, $isFileUpload = false)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 30,
        ]);

        $headers = ['Accept: application/json'];
        
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        if ($data) {
            if ($isFileUpload) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            } else {
                $headers[] = 'Content-Type: application/json';
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            echo "❌ CURL Error: " . curl_error($ch) . "\n";
            curl_close($ch);
            return false;
        }
        
        curl_close($ch);

        echo "📡 HTTP $method $url - Status: $httpCode\n";
        
        $decodedResponse = json_decode($response, true);
        return $decodedResponse;
    }
}

// Example usage
if (php_sapi_name() === 'cli') {
    echo "🧪 Evidence API Test Script\n";
    echo "==========================\n\n";

    $tester = new EvidenceApiTester();

    // You'll need to update these credentials based on your test data
    $phoneNumber = '0700000000';  // Update with actual agent phone number
    $password = 'password';       // Update with actual password

    echo "1. Testing login...\n";
    if ($tester->login($phoneNumber, $password)) {
        
        echo "\n2. Creating test image...\n";
        $imagePath = $tester->createTestImage();
        
        echo "\n3. Uploading evidence...\n";
        $evidence = $tester->uploadEvidence($imagePath, 'DR FORM - Test Upload');
        
        echo "\n4. Viewing all evidence...\n";
        $allEvidence = $tester->viewEvidence();
        
        if ($evidence && isset($evidence['id'])) {
            echo "\n5. Deleting uploaded evidence...\n";
            $tester->deleteEvidence($evidence['id']);
            
            echo "\n6. Viewing evidence after deletion...\n";
            $tester->viewEvidence();
        }
        
        // Clean up test image
        if (file_exists($imagePath)) {
            unlink($imagePath);
            echo "\n🧹 Test image cleaned up\n";
        }
        
    } else {
        echo "\n❌ Cannot proceed without successful login\n";
        echo "Please ensure you have an agent user with phone: $phoneNumber and password: $password\n";
    }

    echo "\n✅ Test completed!\n";
}
