<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_logs', function (Blueprint $table) {
            $table->id();

            // Request Information
            $table->string('endpoint')->index()->comment('API endpoint called');
            $table->string('method', 10)->comment('HTTP method (GET, POST, PUT, DELETE)');
            $table->string('url')->comment('Full URL requested');
            $table->json('headers')->nullable()->comment('Request headers');
            $table->longText('request_payload')->nullable()->comment('Request body/payload');
            $table->json('query_parameters')->nullable()->comment('URL query parameters');

            // Response Information
            $table->integer('response_status')->comment('HTTP response status code');
            $table->longText('response_body')->nullable()->comment('Response body');
            $table->json('response_headers')->nullable()->comment('Response headers');
            $table->integer('response_time_ms')->nullable()->comment('Response time in milliseconds');

            // User and Authentication
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('user_type')->nullable()->comment('Type of user (agent, manager, admin)');
            $table->string('auth_method')->nullable()->comment('Authentication method used');
            $table->string('api_token_id')->nullable()->comment('API token ID if using Sanctum');

            // Request Context
            $table->string('ip_address', 45)->nullable()->comment('Client IP address');
            $table->text('user_agent')->nullable()->comment('Client user agent');
            $table->string('session_id')->nullable()->comment('Session ID if applicable');
            $table->json('location_data')->nullable()->comment('GPS coordinates if provided');

            // Monitoring and Analysis
            $table->enum('log_type', ['evidence', 'vote', 'other'])->default('other')->comment('Type of API call');
            $table->boolean('is_successful')->default(true)->comment('Whether the request was successful');
            $table->text('error_message')->nullable()->comment('Error message if request failed');
            $table->json('metadata')->nullable()->comment('Additional metadata for analysis');

            // Flags and Monitoring
            $table->boolean('is_flagged')->default(false)->comment('Flagged for review');
            $table->string('flag_reason')->nullable()->comment('Reason for flagging');
            $table->timestamp('flagged_at')->nullable();
            $table->foreignId('flagged_by_user_id')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            // Indexes for performance
            $table->index(['endpoint', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['log_type', 'created_at']);
            $table->index(['is_successful', 'created_at']);
            $table->index(['is_flagged', 'created_at']);
            $table->index('response_status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_logs');
    }
};
