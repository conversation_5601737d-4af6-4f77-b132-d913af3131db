<?php

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\Eveidence;
use App\Models\PollingStation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class EvidenceApiTest extends TestCase
{
    use RefreshDatabase;

    protected $agent;
    protected $user;
    protected $pollingStation;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a polling station
        $this->pollingStation = PollingStation::create([
            'name' => 'Test Polling Station',
            'constituency' => 'Test Constituency',
            'district' => 'Test District',
            'county' => 'Test County',
            'subcounty' => 'Test Subcounty',
            'parish' => 'Test Parish',
            'village' => 'Test Village',
        ]);

        // Create an agent user
        $this->user = User::create([
            'name' => 'Test Agent',
            'phone_number' => '0700000000',
            'password' => Hash::make('password'),
            'user_type' => 'agent',
            'is_active' => true,
        ]);

        // Create the agent record
        $this->agent = Agent::create([
            'user_id' => $this->user->id,
            'polling_station_id' => $this->pollingStation->id,
        ]);
    }

    /**
     * Test successful evidence upload via API
     */
    public function test_agent_can_upload_evidence_via_api()
    {
        // Create a fake image file
        Storage::fake('public');
        $file = UploadedFile::fake()->image('evidence.jpg', 800, 600);

        // Login via API first
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $loginResponse->assertStatus(200);
        $token = $loginResponse->json('token');

        // Upload evidence
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/record_envidence', [
            'picture' => $file,
            'file_name' => 'DR FORM',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Evendance uploaded successfully',
                ])
                ->assertJsonStructure([
                    'status',
                    'message',
                    'evedence' => [
                        'id',
                        'file_url',
                        'file_name',
                        'agent_id',
                        'created_at',
                        'updated_at',
                    ]
                ]);

        // Verify evidence was saved to database
        $this->assertDatabaseHas('eveidences', [
            'agent_id' => $this->agent->id,
            'file_name' => 'DR FORM',
        ]);
    }

    /**
     * Test evidence upload without file_name
     */
    public function test_agent_can_upload_evidence_without_file_name()
    {
        Storage::fake('public');
        $file = UploadedFile::fake()->image('evidence.jpg');

        // Login via API
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('token');

        // Upload evidence without file_name
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/record_envidence', [
            'picture' => $file,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Evendance uploaded successfully',
                ]);

        // Verify evidence was saved with null file_name
        $this->assertDatabaseHas('eveidences', [
            'agent_id' => $this->agent->id,
            'file_name' => null,
        ]);
    }

    /**
     * Test evidence upload validation - missing picture
     */
    public function test_evidence_upload_requires_picture()
    {
        // Login via API
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('token');

        // Try to upload without picture
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/record_envidence', [
            'file_name' => 'DR FORM',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['picture']);
    }

    /**
     * Test viewing evidence via API
     */
    public function test_agent_can_view_their_evidence_via_api()
    {
        // Create some evidence records
        $evidence1 = Eveidence::create([
            'agent_id' => $this->agent->id,
            'file_url' => 'test1.jpg',
            'file_name' => 'DR FORM 1',
        ]);

        $evidence2 = Eveidence::create([
            'agent_id' => $this->agent->id,
            'file_url' => 'test2.jpg',
            'file_name' => 'DR FORM 2',
        ]);

        // Login via API
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('token');

        // Get evidence
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/record_envidence');

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Uploaded files',
                ])
                ->assertJsonCount(2, 'evedence')
                ->assertJsonStructure([
                    'status',
                    'message',
                    'evedence' => [
                        '*' => [
                            'id',
                            'file_url',
                            'file_name',
                            'agent_id',
                            'created_at',
                            'updated_at',
                        ]
                    ]
                ]);
    }

    /**
     * Test deleting evidence via API
     */
    public function test_agent_can_delete_evidence_via_api()
    {
        // Create evidence record
        $evidence = Eveidence::create([
            'agent_id' => $this->agent->id,
            'file_url' => 'test.jpg',
            'file_name' => 'DR FORM',
        ]);

        // Login via API
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000000',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('token');

        // Delete evidence
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->deleteJson("/api/record_envidence/{$evidence->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Evendance deleted successfully',
                ]);

        // Verify evidence was deleted
        $this->assertDatabaseMissing('eveidences', [
            'id' => $evidence->id,
        ]);
    }

    /**
     * Test non-agent user cannot upload evidence
     */
    public function test_non_agent_cannot_upload_evidence()
    {
        // Create a manager user
        $manager = User::create([
            'name' => 'Test Manager',
            'phone_number' => '0700000001',
            'password' => Hash::make('password'),
            'user_type' => 'manager',
            'is_active' => true,
        ]);

        Storage::fake('public');
        $file = UploadedFile::fake()->image('evidence.jpg');

        // Login as manager
        $loginResponse = $this->postJson('/api/login', [
            'phone_number' => '0700000001',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('token');

        // Try to upload evidence
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/record_envidence', [
            'picture' => $file,
            'file_name' => 'DR FORM',
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'status' => 'failed',
                    'message' => 'Only Agents can post results',
                ]);
    }

    /**
     * Test unauthenticated user cannot access evidence endpoints
     */
    public function test_unauthenticated_user_cannot_access_evidence_endpoints()
    {
        Storage::fake('public');
        $file = UploadedFile::fake()->image('evidence.jpg');

        // Try to upload without authentication
        $response = $this->postJson('/api/record_envidence', [
            'picture' => $file,
            'file_name' => 'DR FORM',
        ]);

        $response->assertStatus(401);

        // Try to view without authentication
        $response = $this->getJson('/api/record_envidence');
        $response->assertStatus(401);
    }
}
