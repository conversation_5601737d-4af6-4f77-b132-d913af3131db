<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

$admin = User::where('user_type', 'admin')->first();

if (!$admin) {
    $admin = User::create([
        'name' => 'Admin User',
        'phone_number' => '0700000001',
        'password' => Hash::make('password'),
        'user_type' => 'admin',
        'is_active' => true,
    ]);
    echo "Created admin user: {$admin->name} (ID: {$admin->id})\n";
} else {
    echo "Admin user exists: {$admin->name} (ID: {$admin->id})\n";
}

echo "Admin login: phone_number=0700000001, password=password\n";
