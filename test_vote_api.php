<?php

/**
 * Test Vote API to generate logs
 */

require_once 'test_evidence_api.php';

echo "🗳️  Vote API Test Script\n";
echo "======================\n\n";

$tester = new EvidenceApiTester();

// Login credentials
$phoneNumber = '0700000000';
$password = 'password';

echo "1. Testing login...\n";
if ($tester->login($phoneNumber, $password)) {
    
    echo "\n2. Testing vote submission...\n";
    
    // Test vote submission
    $url = 'http://localhost:8000/api/record_votes';
    
    $voteData = [
        'candidate_id' => 1,
        'number_of_votes' => 150,
        'latitude' => 0.3476,
        'longitude' => 32.5825
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $tester->token,
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_POSTFIELDS => json_encode($voteData),
        CURLOPT_TIMEOUT => 30,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📡 HTTP POST $url - Status: $httpCode\n";
    
    if ($httpCode === 200) {
        $decoded = json_decode($response, true);
        echo "✅ Vote submitted successfully!\n";
        if (isset($decoded['data'])) {
            echo "   Vote ID: " . $decoded['data']['id'] . "\n";
            echo "   Candidate ID: " . $decoded['data']['candidate_id'] . "\n";
            echo "   Number of Votes: " . $decoded['data']['number_of_votes'] . "\n";
        }
    } else {
        echo "❌ Vote submission failed: $response\n";
    }
    
    echo "\n3. Testing another vote submission (update)...\n";
    
    // Test updating the same vote
    $voteData['number_of_votes'] = 175;
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $tester->token,
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_POSTFIELDS => json_encode($voteData),
        CURLOPT_TIMEOUT => 30,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📡 HTTP POST $url - Status: $httpCode\n";
    
    if ($httpCode === 200) {
        $decoded = json_decode($response, true);
        echo "✅ Vote updated successfully!\n";
        if (isset($decoded['action'])) {
            echo "   Action: " . $decoded['action'] . "\n";
        }
    } else {
        echo "❌ Vote update failed: $response\n";
    }
    
    echo "\n4. Testing invalid vote submission...\n";
    
    // Test with invalid data
    $invalidData = [
        'candidate_id' => 999, // Non-existent candidate
        'number_of_votes' => -10, // Invalid vote count
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $tester->token,
            'Content-Type: application/json',
            'Accept: application/json'
        ],
        CURLOPT_POSTFIELDS => json_encode($invalidData),
        CURLOPT_TIMEOUT => 30,
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "📡 HTTP POST $url - Status: $httpCode\n";
    
    if ($httpCode !== 200) {
        echo "✅ Invalid vote correctly rejected (HTTP $httpCode)\n";
    } else {
        echo "❌ Invalid vote was accepted (unexpected)\n";
    }
    
} else {
    echo "\n❌ Cannot proceed without successful login\n";
}

echo "\n✅ Vote API test completed!\n";
echo "\n📊 Check the API logs dashboard at: http://localhost:8000/admin/api-logs\n";
