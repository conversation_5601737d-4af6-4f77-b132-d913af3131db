<?php

/**
 * Production Evidence API Test Script
 * 
 * This script tests the Evidence API on the production server
 * URL: https://ugelections.live/api/record_envidence
 */

class ProductionEvidenceApiTester
{
    private $baseUrl;
    public $token;

    public function __construct($baseUrl = 'https://ugelections.live')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Login and get authentication token
     */
    public function login($phoneNumber, $password)
    {
        $url = $this->baseUrl . '/api/login';
        
        $data = [
            'phone_number' => $phoneNumber,
            'password' => $password
        ];

        echo "🔐 Attempting login to: $url\n";
        echo "📱 Phone: $phoneNumber\n";

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response && isset($response['authorization']['token'])) {
            $this->token = $response['authorization']['token'];
            echo "✅ Login successful!\n";
            echo "🎫 Token: " . substr($this->token, 0, 20) . "...\n";
            
            // Display user info if available
            if (isset($response['user'])) {
                echo "👤 User: " . $response['user']['name'] . "\n";
                echo "🏷️  Type: " . $response['user']['user_type'] . "\n";
            }
            
            return true;
        } else {
            echo "❌ Login failed!\n";
            echo "📄 Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
            return false;
        }
    }

    /**
     * Upload evidence image
     */
    public function uploadEvidence($imagePath, $fileName = null)
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        if (!file_exists($imagePath)) {
            echo "❌ Image file not found: $imagePath\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence';
        
        echo "\n📤 Uploading evidence to: $url\n";
        echo "📁 File: $imagePath (" . round(filesize($imagePath) / 1024, 2) . " KB)\n";
        if ($fileName) {
            echo "🏷️  Name: $fileName\n";
        }

        $postData = [
            'picture' => new CURLFile($imagePath, mime_content_type($imagePath), basename($imagePath))
        ];

        if ($fileName) {
            $postData['file_name'] = $fileName;
        }

        $response = $this->makeRequest('POST', $url, $postData, true);
        
        if ($response && isset($response['status']) && $response['status'] === 'success') {
            echo "✅ Evidence uploaded successfully!\n";
            if (isset($response['evedence'])) {
                echo "🆔 Evidence ID: " . $response['evedence']['id'] . "\n";
                echo "🔗 File URL: " . $response['evedence']['file_url'] . "\n";
                echo "📝 File Name: " . ($response['evedence']['file_name'] ?? 'N/A') . "\n";
                echo "🕒 Created: " . $response['evedence']['created_at'] . "\n";
            }
            return $response['evedence'] ?? true;
        } else {
            echo "❌ Evidence upload failed!\n";
            echo "📄 Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
            return false;
        }
    }

    /**
     * View all evidence for the authenticated agent
     */
    public function viewEvidence()
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence';
        
        echo "\n📋 Retrieving evidence from: $url\n";
        
        $response = $this->makeRequest('GET', $url);
        
        if ($response && isset($response['status']) && $response['status'] === 'success') {
            echo "✅ Evidence retrieved successfully!\n";
            echo "📊 Total files: " . count($response['evedence']) . "\n";
            
            foreach ($response['evedence'] as $index => $evidence) {
                echo "\n📄 Evidence #" . ($index + 1) . ":\n";
                echo "   🆔 ID: " . $evidence['id'] . "\n";
                echo "   🔗 File URL: " . $evidence['file_url'] . "\n";
                echo "   📝 File Name: " . ($evidence['file_name'] ?? 'N/A') . "\n";
                echo "   🕒 Created: " . $evidence['created_at'] . "\n";
                echo "   🔍 Full URL: " . $this->baseUrl . "/files/" . $evidence['file_url'] . "\n";
            }
            
            return $response['evedence'];
        } else {
            echo "❌ Failed to retrieve evidence!\n";
            echo "📄 Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
            return false;
        }
    }

    /**
     * Delete evidence by ID
     */
    public function deleteEvidence($evidenceId)
    {
        if (!$this->token) {
            echo "❌ Please login first\n";
            return false;
        }

        $url = $this->baseUrl . '/api/record_envidence/' . $evidenceId;
        
        echo "\n🗑️  Deleting evidence from: $url\n";
        
        $response = $this->makeRequest('DELETE', $url);
        
        if ($response && isset($response['status']) && $response['status'] === 'success') {
            echo "✅ Evidence deleted successfully!\n";
            return true;
        } else {
            echo "❌ Failed to delete evidence!\n";
            echo "📄 Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n";
            return false;
        }
    }

    /**
     * Create a test image file
     */
    public function createTestImage($filename = 'production_test_evidence.jpg')
    {
        // Create a test image with production-specific content
        $image = imagecreate(800, 600);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        $blue = imagecolorallocate($image, 0, 100, 200);
        $green = imagecolorallocate($image, 0, 150, 0);
        
        // Draw border
        imagerectangle($image, 10, 10, 790, 590, $black);
        
        // Title
        imagestring($image, 5, 200, 30, 'UGANDA ELECTIONS - DR FORM', $blue);
        imagestring($image, 4, 250, 60, 'EVIDENCE SUBMISSION', $black);
        
        // Form details
        imagestring($image, 3, 50, 120, 'Production API Test - ugelections.live', $green);
        imagestring($image, 3, 50, 150, 'Polling Station: Test Station', $black);
        imagestring($image, 3, 50, 180, 'Constituency: Test Constituency', $black);
        imagestring($image, 3, 50, 210, 'District: Test District', $black);
        
        // Results section
        imagestring($image, 4, 50, 260, 'PRESIDENTIAL RESULTS:', $blue);
        imagestring($image, 3, 50, 290, 'Candidate A: 245 votes', $black);
        imagestring($image, 3, 50, 320, 'Candidate B: 189 votes', $black);
        imagestring($image, 3, 50, 350, 'Candidate C: 156 votes', $black);
        imagestring($image, 3, 50, 380, 'Invalid Votes: 12', $black);
        imagestring($image, 3, 50, 410, 'Total Votes: 602', $black);
        
        // Signature section
        imagestring($image, 3, 50, 460, 'Agent Signature: ________________', $black);
        imagestring($image, 3, 50, 490, 'Date: ' . date('Y-m-d'), $black);
        imagestring($image, 3, 50, 520, 'Time: ' . date('H:i:s'), $black);
        
        // Footer
        imagestring($image, 2, 50, 560, 'Generated by Production API Test - ' . date('Y-m-d H:i:s'), $black);
        
        imagejpeg($image, $filename, 90);
        imagedestroy($image);
        
        echo "✅ Test image created: $filename\n";
        echo "📏 Image size: " . round(filesize($filename) / 1024, 2) . " KB\n";
        return $filename;
    }

    /**
     * Test API connectivity and SSL
     */
    public function testConnectivity()
    {
        echo "🌐 Testing connectivity to: " . $this->baseUrl . "\n";
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . '/api/login',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode(['test' => 'connectivity']),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_TIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $sslVerifyResult = curl_getinfo($ch, CURLINFO_SSL_VERIFYRESULT);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        
        if (curl_error($ch)) {
            echo "❌ Connection error: " . curl_error($ch) . "\n";
            curl_close($ch);
            return false;
        }
        
        curl_close($ch);
        
        echo "✅ Connection successful!\n";
        echo "📊 HTTP Status: $httpCode\n";
        echo "🔒 SSL Verify: " . ($sslVerifyResult === 0 ? 'Valid' : 'Invalid') . "\n";
        echo "⏱️  Response Time: " . round($totalTime * 1000, 2) . "ms\n";
        
        return true;
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = null, $isFileUpload = false)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_TIMEOUT => 30,
        ]);

        $headers = ['Accept: application/json'];
        
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        if ($data) {
            if ($isFileUpload) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            } else {
                $headers[] = 'Content-Type: application/json';
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
        
        if (curl_error($ch)) {
            echo "❌ CURL Error: " . curl_error($ch) . "\n";
            curl_close($ch);
            return false;
        }
        
        curl_close($ch);

        echo "📡 HTTP $method - Status: $httpCode - Time: " . round($totalTime * 1000, 2) . "ms\n";
        
        $decodedResponse = json_decode($response, true);
        return $decodedResponse;
    }
}

// Example usage
if (php_sapi_name() === 'cli') {
    echo "🇺🇬 Uganda Elections - Production Evidence API Test\n";
    echo "================================================\n\n";

    $tester = new ProductionEvidenceApiTester();

    echo "1. Testing connectivity...\n";
    if (!$tester->testConnectivity()) {
        echo "❌ Cannot connect to production server. Exiting.\n";
        exit(1);
    }

    echo "\n2. Login (you'll need valid credentials)...\n";
    echo "Enter agent phone number: ";
    $phoneNumber = trim(fgets(STDIN));
    echo "Enter password: ";
    $password = trim(fgets(STDIN));

    if ($tester->login($phoneNumber, $password)) {
        
        echo "\n3. Creating test evidence image...\n";
        $imagePath = $tester->createTestImage();
        
        echo "\n4. Uploading evidence...\n";
        $evidence = $tester->uploadEvidence($imagePath, 'DR FORM - Production Test');
        
        echo "\n5. Viewing all evidence...\n";
        $allEvidence = $tester->viewEvidence();
        
        if ($evidence && isset($evidence['id'])) {
            echo "\n6. Do you want to delete the test evidence? (y/N): ";
            $delete = trim(fgets(STDIN));
            
            if (strtolower($delete) === 'y') {
                $tester->deleteEvidence($evidence['id']);
            } else {
                echo "ℹ️  Test evidence kept with ID: " . $evidence['id'] . "\n";
            }
        }
        
        // Clean up test image
        if (file_exists($imagePath)) {
            unlink($imagePath);
            echo "\n🧹 Test image cleaned up\n";
        }
        
    } else {
        echo "\n❌ Cannot proceed without successful login\n";
        echo "Please ensure you have valid agent credentials for ugelections.live\n";
    }

    echo "\n✅ Production API test completed!\n";
    echo "📊 Check the API logs dashboard at: https://ugelections.live/admin/api-logs\n";
}
